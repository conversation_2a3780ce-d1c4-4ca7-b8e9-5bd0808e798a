import { createSignal, createEffect, onMount, onCleanup, Show } from 'solid-js';
import { EditorView, basicSetup } from 'codemirror';
import { EditorState } from '@codemirror/state';
import { markdown } from '@codemirror/lang-markdown';
import { oneDark } from '@codemirror/theme-one-dark';
import { Save, X, Eye, Edit, Download, Type, Image as ImageIcon, Upload } from 'lucide-solid';
import { Button, toast, Input } from '../ui';
import { useTheme } from '../../stores/ThemeContext';
import { marked } from 'marked';
import '../../styles/md.css';

const MarkdownEditor = (props) => {
  const { isDark } = useTheme();

  const [content, setContent] = createSignal('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = createSignal(false);
  const [isLoading, setIsLoading] = createSignal(false);
  const [isSaving, setIsSaving] = createSignal(false);
  const [showPreview, setShowPreview] = createSignal(false);
  const [editorInitialized, setEditorInitialized] = createSignal(false);

  // 重命名相关状态
  const [isRenaming, setIsRenaming] = createSignal(false);
  const [newFileName, setNewFileName] = createSignal('');

  // 图片选择器相关状态
  const [showImagePicker, setShowImagePicker] = createSignal(false);
  const [imageFiles, setImageFiles] = createSignal([]);

  // 自动保存相关状态
  const [isAutoSaving, setIsAutoSaving] = createSignal(false);
  const [lastSaveTime, setLastSaveTime] = createSignal(null);

  // 图片上传相关状态
  const [isDragOver, setIsDragOver] = createSignal(false);
  const [isUploading, setIsUploading] = createSignal(false);
  const [uploadProgress, setUploadProgress] = createSignal(0);

  let editorRef;
  let editorView;
  let autoSaveTimerRef = null;

  // 配置 marked 选项
  marked.setOptions({
    gfm: true, // 启用 GitHub 风格的 Markdown
    breaks: true, // 支持换行符转换为 <br>
    tables: true, // 启用表格支持
    sanitize: false, // 允许 HTML（注意：在生产环境中可能需要更严格的配置）
  });

  // 渲染 Markdown 内容
  const renderMarkdown = (text, enableImages = true) => {
    try {
      let processedText = text;

      // 只在预览模式下处理图片路径
      if (enableImages) {
        // 处理图片路径：将相对路径转换为完整的 API 路径
        processedText = text.replace(
          /!\[([^\]]*)\]\((?!https?:\/\/)([^)]+)\)/g,
          `![$1](${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173'}/api/papers/${props.paperId}/files/$2)`
        );
      } else {
        // 编辑模式下，将图片标记替换为占位符，避免加载图片
        processedText = text.replace(
          /!\[([^\]]*)\]\(([^)]+)\)/g,
          `![图片: $1]($2)`
        );
      }

      return marked(processedText);
    } catch (error) {
      console.error('Markdown 渲染失败:', error);
      return `<pre>${text}</pre>`;
    }
  };

  // 获取主题相关的编辑器扩展
  const getThemeExtensions = () => {
    const isCurrentlyDark = isDark();

    if (isCurrentlyDark) {
      // 深色主题
      return [
        oneDark,
        EditorView.theme({
          '&': {
            color: 'rgb(var(--text-primary))',
            backgroundColor: 'rgb(var(--bg-primary))',
            height: '100%'
          },
          '.cm-content': {
            color: 'rgb(var(--text-primary))',
            backgroundColor: 'rgb(var(--bg-primary))',
            caretColor: 'rgb(var(--text-primary))',
            padding: '12px',
            minHeight: '100%',
            fontSize: '16px',
            lineHeight: '1.6'
          },
          '.cm-editor': {
            color: 'rgb(var(--text-primary))',
            backgroundColor: 'rgb(var(--bg-primary))',
            height: '100%'
          },
          '.cm-scroller': {
            overflow: 'auto',
            height: '100%'
          },
          '.cm-focused': {
            outline: 'none'
          },
          '.cm-editor.cm-focused': {
            outline: 'none'
          }
        })
      ];
    } else {
      // 浅色主题
      return [
        EditorView.theme({
          '&': {
            color: 'rgb(var(--text-primary))',
            backgroundColor: 'rgb(var(--bg-primary))',
            height: '100%'
          },
          '.cm-content': {
            color: 'rgb(var(--text-primary))',
            backgroundColor: 'rgb(var(--bg-primary))',
            caretColor: 'rgb(var(--text-primary))',
            padding: '12px',
            minHeight: '100%',
            fontSize: '16px',
            lineHeight: '1.6'
          },
          '.cm-editor': {
            color: 'rgb(var(--text-primary))',
            backgroundColor: 'rgb(var(--bg-primary))',
            height: '100%'
          },
          '.cm-scroller': {
            overflow: 'auto',
            height: '100%'
          },
          '.cm-focused': {
            outline: 'none'
          },
          '.cm-editor.cm-focused': {
            outline: 'none'
          },
          '.cm-line': {
            color: 'rgb(var(--text-primary))'
          }
        })
      ];
    }
  };

  // 加载文件内容
  const loadFileContent = async () => {
    if (!props.file) return;

    setIsLoading(true);
    console.log('开始加载文件内容:', props.file.name);

    // 如果是新建文件，直接设置默认内容
    if (props.file.isNew) {
      const defaultContent = `# ${props.file.name.replace('.md', '')}

## 笔记内容

在这里开始编写您的笔记...

### 提示
- 支持Markdown语法
- 使用 Ctrl+S 保存
- 可以切换预览模式查看效果

---

*创建时间：${new Date().toLocaleString()}*`;

      setContent(defaultContent);
      setHasUnsavedChanges(true); // 新建文件默认有未保存更改
      setIsLoading(false);
      return;
    }

    try {
      // 这里需要调用API获取文件内容
      const url = `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173'}/api/papers/${props.paperId}/files/${props.file.name}`;
      console.log('请求URL:', url);

      const response = await fetch(url);

      if (response.ok) {
        const text = await response.text();
        console.log('文件内容加载成功，长度:', text.length);
        setContent(text);
        setHasUnsavedChanges(false);
      } else {
        console.warn('API响应失败，状态:', response.status);
        // 如果API不存在，使用模拟内容进行测试
        const mockContent = `# ${props.file.name}

这是一个示例Markdown文件。

## 功能特性

- 支持Markdown语法高亮
- 支持编辑和预览模式切换
- 支持保存功能
- 支持快捷键 Ctrl+S

## 代码示例

\`\`\`javascript
console.log('Hello, World!');
\`\`\`

您可以编辑这个内容来测试编辑器功能。`;

        console.log('使用模拟内容');
        setContent(mockContent);
        setHasUnsavedChanges(false);
      }
    } catch (error) {
      console.error('加载文件内容失败:', error);
      toast.warning('API不可用，使用示例内容');

      // 使用示例内容
      const exampleContent = `# 示例文档

这是一个示例Markdown文档，用于测试编辑器功能。

## 编辑器功能

- ✅ 语法高亮
- ✅ 编辑模式
- ✅ 预览模式
- ✅ 保存功能

请开始编辑...`;

      setContent(exampleContent);
      setHasUnsavedChanges(false);
    } finally {
      setIsLoading(false);
    }
  };

  // 保存文件内容
  const saveFileContent = async () => {
    if (!props.file || !hasUnsavedChanges()) return;

    setIsSaving(true);
    try {
      // 检查是否为新建文件
      if (props.file.isNew) {
        // 新建文件，使用POST方法
        const formData = new FormData();
        const blob = new Blob([content()], { type: 'text/markdown' });
        formData.append('file', blob, props.file.name);

        const response = await fetch(
          `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173'}/api/papers/${props.paperId}/files`,
          {
            method: 'POST',
            body: formData
          }
        );

        if (response.ok) {
          setHasUnsavedChanges(false);
          // 移除新建标记
          props.file.isNew = false;
          setLastSaveTime(new Date());
          clearAutoSaveTimer();
          toast.success('笔记创建成功');
        } else {
          throw new Error('Failed to create new file');
        }
      } else {
        // 现有文件，使用覆盖上传的方式更新内容
        // 根据API文档，同名文件可以直接覆盖
        const formData = new FormData();
        const blob = new Blob([content()], { type: 'text/markdown' });
        formData.append('file', blob, props.file.name);

        const response = await fetch(
          `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173'}/api/papers/${props.paperId}/files?type=note`,
          {
            method: 'POST',
            body: formData
          }
        );

        if (response.ok) {
          setHasUnsavedChanges(false);
          setLastSaveTime(new Date());
          clearAutoSaveTimer();
          toast.success('文件保存成功');
        } else {
          throw new Error('Failed to save file content');
        }
      }
    } catch (error) {
      console.error('保存文件失败:', error);
      if (props.file.isNew) {
        toast.error('创建笔记失败');
      } else {
        toast.error('保存文件失败');
      }
    } finally {
      setIsSaving(false);
    }
  };

  // 自动保存函数
  const autoSave = async () => {
    if (!hasUnsavedChanges() || isSaving() || isAutoSaving()) {
      return;
    }

    setIsAutoSaving(true);
    try {
      // 检查是否为新建文件
      if (props.file.isNew) {
        // 新建文件，使用POST方法
        const formData = new FormData();
        const blob = new Blob([content()], { type: 'text/markdown' });
        formData.append('file', blob, props.file.name);

        const response = await fetch(
          `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173'}/api/papers/${props.paperId}/files`,
          {
            method: 'POST',
            body: formData
          }
        );

        if (response.ok) {
          setHasUnsavedChanges(false);
          // 移除新建标记
          props.file.isNew = false;
          setLastSaveTime(new Date());
          console.log('自动保存成功 - 新建文件');
        } else {
          throw new Error('Failed to auto-save new file');
        }
      } else {
        // 现有文件，使用覆盖上传的方式更新内容
        const formData = new FormData();
        const blob = new Blob([content()], { type: 'text/markdown' });
        formData.append('file', blob, props.file.name);

        const response = await fetch(
          `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173'}/api/papers/${props.paperId}/files?type=note`,
          {
            method: 'POST',
            body: formData
          }
        );

        if (response.ok) {
          setHasUnsavedChanges(false);
          setLastSaveTime(new Date());
          console.log('自动保存成功 - 现有文件');
        } else {
          throw new Error('Failed to auto-save file content');
        }
      }
    } catch (error) {
      console.error('自动保存失败:', error);
      // 自动保存失败时不显示错误提示，避免干扰用户
    } finally {
      setIsAutoSaving(false);
    }
  };

  // 启动自动保存定时器
  const startAutoSaveTimer = () => {
    // 清除现有定时器
    if (autoSaveTimerRef) {
      clearTimeout(autoSaveTimerRef);
    }

    // 设置新的定时器（3秒后自动保存）
    autoSaveTimerRef = setTimeout(() => {
      autoSave();
    }, 3000);
  };

  // 清除自动保存定时器
  const clearAutoSaveTimer = () => {
    if (autoSaveTimerRef) {
      clearTimeout(autoSaveTimerRef);
      autoSaveTimerRef = null;
    }
  };

  // 开始重命名文件
  const handleStartRename = () => {
    if (!props.file) return;
    setIsRenaming(true);
    // 设置当前文件名（去掉扩展名）
    const nameWithoutExt = props.file.name.replace(/\.[^/.]+$/, '');
    setNewFileName(nameWithoutExt);
  };

  // 取消重命名
  const handleCancelRename = () => {
    setIsRenaming(false);
    setNewFileName('');
  };

  // 确认重命名
  const handleConfirmRename = async () => {
    const trimmedName = newFileName().trim();

    if (!props.file || !trimmedName) {
      toast.warning('请输入有效的文件名');
      return;
    }

    // 获取原文件扩展名
    const originalExt = props.file.name.substring(props.file.name.lastIndexOf('.'));
    const finalNewName = trimmedName + originalExt;

    // 如果名称没有变化，直接取消
    if (finalNewName === props.file.name) {
      handleCancelRename();
      return;
    }

    try {
      // 调用重命名API
      const { filesAPI } = await import('../../services/api');
      await filesAPI.rename(props.paperId, props.file.name, finalNewName);

      // 重命名成功
      toast.success('文件重命名成功');

      // 更新文件对象的名称
      props.file.name = finalNewName;

      // 通知父组件刷新文件列表（如果有回调）
      if (props.onFileRenamed) {
        props.onFileRenamed();
      }

      // 重置状态
      handleCancelRename();
    } catch (error) {
      console.error('重命名文件失败:', error);
      toast.error('重命名失败，请重试');
    }
  };

  // 处理重命名输入框的键盘事件
  const handleRenameKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleConfirmRename();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelRename();
    }
  };

  // 加载图片文件列表
  const loadImageFiles = async () => {
    if (!props.paperId) return;

    try {
      // 这里应该调用API获取图片文件列表
      // 暂时使用props中的图片文件（如果有的话）
      if (props.imageFiles) {
        setImageFiles(props.imageFiles);
      }
    } catch (error) {
      console.error('加载图片文件失败:', error);
    }
  };

  // 打开图片选择器
  const handleOpenImagePicker = () => {
    loadImageFiles();
    setShowImagePicker(true);
  };

  // 关闭图片选择器
  const handleCloseImagePicker = () => {
    setShowImagePicker(false);
  };

  // 插入图片到编辑器
  const insertImageToEditor = (imageName, altText = '') => {
    if (!editorView) return;

    const imageMarkdown = `![${altText || imageName}](${imageName})`;

    // 获取当前光标位置
    const { from } = editorView.state.selection.main;

    // 插入图片 Markdown 语法
    editorView.dispatch({
      changes: {
        from,
        insert: imageMarkdown
      },
      selection: { anchor: from + imageMarkdown.length }
    });

    // 更新内容状态
    setContent(editorView.state.doc.toString());
    setHasUnsavedChanges(true);

    // 关闭图片选择器
    handleCloseImagePicker();

    toast.success('图片已插入');
  };

  // 处理图片上传
  const handleImageUpload = async (files) => {
    if (!files || files.length === 0) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        // 检查文件类型
        if (!file.type.startsWith('image/')) {
          toast.error(`${file.name} 不是有效的图片文件`);
          continue;
        }

        // 检查文件大小（限制为10MB）
        if (file.size > 10 * 1024 * 1024) {
          toast.error(`${file.name} 文件过大，请选择小于10MB的图片`);
          continue;
        }

        // 创建FormData
        const formData = new FormData();
        formData.append('file', file);

        // 上传文件
        const response = await fetch(
          `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173'}/api/papers/${props.paperId}/files?type=image`,
          {
            method: 'POST',
            body: formData
          }
        );

        if (response.ok) {
          // 上传成功，插入图片到编辑器
          const altText = file.name.replace(/\.[^/.]+$/, ''); // 移除扩展名作为alt文本
          insertImageToEditor(file.name, altText);

          toast.success(`图片 ${file.name} 上传成功`);
        } else {
          throw new Error(`Failed to upload ${file.name}`);
        }

        // 更新进度
        setUploadProgress(Math.round(((i + 1) / files.length) * 100));
      }
      // 所有文件上传完成后，通知父组件刷新文件列表
      if (props.onFileCreated) {
        props.onFileCreated();
      }
    } catch (error) {
      console.error('图片上传失败:', error);
      toast.error('图片上传失败，请重试');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  // 处理拖拽事件
  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    handleImageUpload(files);
  };

  // 处理文件选择
  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    handleImageUpload(files);
    // 清空input值，允许重复选择同一文件
    e.target.value = '';
  };

  // 打开文件选择对话框
  const openFileDialog = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.multiple = true;
    input.onchange = handleFileSelect;
    input.click();
  };

  // 初始化CodeMirror编辑器
  const initEditor = () => {
    if (!editorRef) {
      console.log('editorRef不存在，跳过初始化');
      return;
    }

    // 如果编辑器已经初始化，则不重新初始化
    if (editorInitialized() && editorView) {
      console.log('编辑器已初始化，跳过重复初始化');
      return;
    }

    // 清理之前的编辑器实例
    if (editorView) {
      console.log('清理之前的编辑器实例');
      editorView.destroy();
      editorView = null;
    }

    // 清空容器
    editorRef.innerHTML = '';

    try {
      const state = EditorState.create({
        doc: content(),
        extensions: [
          basicSetup,
          markdown(),
          ...getThemeExtensions(),
          EditorView.lineWrapping, // 添加自动换行
          EditorView.updateListener.of((update) => {
            if (update.docChanged) {
              const newContent = update.state.doc.toString();
              setContent(newContent);
              setHasUnsavedChanges(true);

              // 启动自动保存定时器
              startAutoSaveTimer();
            }
          })
        ]
      });

      editorView = new EditorView({
        state,
        parent: editorRef
      });

      setEditorInitialized(true);
      console.log('编辑器初始化完成，内容长度:', content().length);
    } catch (error) {
      console.error('编辑器初始化失败:', error);
    }
  };



  // 键盘快捷键处理
  const handleKeyDown = (e) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
      e.preventDefault();
      saveFileContent();
    }
  };

  // 监听文件变化，重新加载内容
  createEffect(() => {
    if (props.file) {
      setEditorInitialized(false); // 重置编辑器状态
      loadFileContent();
    }
  });

  // 监听图片文件列表变化
  createEffect(() => {
    if (props.imageFiles) {
      setImageFiles(props.imageFiles);
    }
  });

  // 监听内容加载完成和编辑模式，初始化编辑器
  createEffect(() => {
    if (content() !== '' && !isLoading() && editorRef && !editorInitialized()) {
      console.log('初始化编辑器');
      setTimeout(() => {
        initEditor();
      }, 100);
    }
  });

  onMount(() => {
    document.addEventListener('keydown', handleKeyDown);
    // 不在这里初始化编辑器，等内容加载后再初始化
  });

  onCleanup(() => {
    if (editorView) {
      editorView.destroy();
    }
    document.removeEventListener('keydown', handleKeyDown);
    // 清除自动保存定时器
    clearAutoSaveTimer();
  });

  return (
    <div class="flex flex-col h-full bg-theme-primary overflow-hidden">
        {/* 编辑器工具栏 */}
      <div class="flex items-center justify-between p-3 border-b border-theme-primary min-w-0">
        <div class="flex items-center space-x-3 min-w-0 flex-1">
          <div class="flex items-center space-x-2 min-w-0 flex-shrink">
            <Edit size={16} class="text-blue-500 flex-shrink-0" />
            <Show when={!isRenaming()}>
              <h3 class="text-sm font-semibold text-theme-primary truncate">
                {props.file?.name || '编辑器'}
              </h3>
            </Show>
            <Show when={isRenaming()}>
              <div class="flex items-center space-x-1">
                <Input
                  value={newFileName()}
                  onInput={(e) => setNewFileName(e.target.value)}
                  onKeyPress={handleRenameKeyPress}
                  class="text-sm h-7 px-2 w-32 min-w-0"
                  placeholder="文件名"
                  autofocus
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleConfirmRename}
                  class="h-7 w-7 p-1 text-green-600 hover:text-green-700 flex-shrink-0"
                  title="确认重命名"
                >
                  ✓
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancelRename}
                  class="h-7 w-7 p-1 text-gray-500 hover:text-gray-700 flex-shrink-0"
                  title="取消重命名"
                >
                  ✕
                </Button>
              </div>
            </Show>
          </div>

          {/* 保存状态指示器 - 独立区域 */}
          <div class="flex items-center space-x-2 flex-shrink-0">
            <Show when={isUploading()}>
              <span class="text-xs text-blue-500 whitespace-nowrap">上传中... {uploadProgress()}%</span>
            </Show>
            <Show when={hasUnsavedChanges() && !isUploading()}>
              <span class="text-xs text-orange-500 whitespace-nowrap">未保存</span>
            </Show>
            <Show when={isAutoSaving() && !isUploading()}>
              <span class="text-xs text-blue-500 whitespace-nowrap">自动保存中...</span>
            </Show>
            <Show when={lastSaveTime() && !hasUnsavedChanges() && !isAutoSaving() && !isUploading()}>
              <span class="text-xs text-green-500 whitespace-nowrap">
                已保存 {new Date(lastSaveTime()).toLocaleTimeString()}
              </span>
            </Show>
          </div>
        </div>
        
        <div class="flex items-center space-x-2 flex-shrink-0">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowPreview(!showPreview())}
            class="text-theme-secondary hover:text-theme-primary flex-shrink-0"
            title={showPreview() ? "编辑模式" : "预览模式"}
          >
            {showPreview() ? <Edit size={14} /> : <Eye size={14} />}
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={saveFileContent}
            disabled={!hasUnsavedChanges() || isSaving()}
            class="text-green-600 hover:text-green-700 disabled:text-theme-muted flex-shrink-0"
            title="保存 (Ctrl+S)"
          >
            <Save size={14} />
          </Button>

          <Show when={!isRenaming()}>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleStartRename}
              class="text-orange-600 hover:text-orange-700 flex-shrink-0"
              title="重命名文件"
            >
              <Type size={14} />
            </Button>
          </Show>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleOpenImagePicker}
            class="text-purple-600 hover:text-purple-700 flex-shrink-0"
            title="插入图片"
          >
            <ImageIcon size={14} />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={openFileDialog}
            disabled={isUploading()}
            class="text-green-600 hover:text-green-700 disabled:text-gray-400 flex-shrink-0"
            title="上传图片"
          >
            <Upload size={14} />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => props.onFileDownload?.(props.file)}
            class="text-blue-600 hover:text-blue-700 flex-shrink-0"
            title="下载文件"
          >
            <Download size={14} />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={props.onClose}
            class="text-theme-secondary hover:text-theme-primary flex-shrink-0"
            title="关闭编辑器"
          >
            <X size={16} />
          </Button>
        </div>
      </div>

      {/* 编辑器内容区域 */}
      <div class="flex-1 flex overflow-hidden">
        <Show when={isLoading()}>
          <div class="flex-1 flex items-center justify-center">
            <div class="text-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p class="text-sm text-gray-500">正在加载文件内容...</p>
            </div>
          </div>
        </Show>

        <Show when={!isLoading()}>
          {/* 预览区域 */}
          <Show when={showPreview()}>
            <div class="flex-1 p-4 bg-theme-primary overflow-auto">
              <div
                class="prose prose-sm max-w-none dark:prose-invert markdown-preview"
                innerHTML={renderMarkdown(content(), true)}
              />
            </div>
          </Show>

          {/* 编辑器区域 */}
          <div
            class="flex-1 flex flex-col overflow-hidden relative"
            style={{ display: showPreview() ? 'none' : 'block' }}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <div
              ref={editorRef}
              class="flex-1 bg-theme-primary text-theme-primary"
              style={{ height: '100%', overflow: 'auto' }}
            />

            {/* 拖拽上传覆盖层 */}
            <Show when={isDragOver()}>
              <div class="absolute inset-0 bg-blue-500 bg-opacity-20 border-2 border-dashed border-blue-500 flex items-center justify-center z-10">
                <div class="text-center">
                  <Upload size={48} class="mx-auto text-blue-500 mb-2" />
                  <p class="text-blue-700 font-medium">拖拽图片到这里上传</p>
                  <p class="text-blue-600 text-sm">支持 JPG、PNG、GIF 等格式</p>
                </div>
              </div>
            </Show>

            {/* 上传进度覆盖层 */}
            <Show when={isUploading()}>
              <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20">
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 text-center">
                  <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                  <p class="text-gray-900 dark:text-white font-medium mb-2">正在上传图片...</p>
                  <div class="w-48 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      class="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress()}%` }}
                    ></div>
                  </div>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">{uploadProgress()}%</p>
                </div>
              </div>
            </Show>
          </div>
        </Show>
      </div>

      {/* 图片选择器模态框 */}
      <Show when={showImagePicker()}>
        <div class="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
            {/* 模态框头部 */}
            <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">选择图片</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCloseImagePicker}
                class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X size={20} />
              </Button>
            </div>

            {/* 图片列表 */}
            <div class="p-4 overflow-auto max-h-96">
              <Show when={imageFiles().length > 0}>
                <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-3">
                  <For each={imageFiles()}>
                    {(file) => (
                      <div
                        class="relative group cursor-pointer bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden hover:ring-2 hover:ring-purple-500 transition-all"
                        onClick={() => insertImageToEditor(file.name, file.name.replace(/\.[^/.]+$/, ''))}
                        title={`插入图片: ${file.name}`}
                      >
                        <div class="aspect-square">
                          <img
                            src={`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173'}/api/papers/${props.paperId}/files/${file.name}`}
                            alt={file.name}
                            class="w-full h-full object-cover"
                            onError={(e) => {
                              e.target.style.display = 'none';
                              e.target.nextElementSibling.style.display = 'flex';
                            }}
                          />
                          <div class="w-full h-full hidden items-center justify-center">
                            <ImageIcon size={24} class="text-gray-400" />
                          </div>
                        </div>
                        <div class="absolute inset-x-0 bottom-0 bg-black bg-opacity-60 text-white text-xs p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <p class="truncate">{file.name}</p>
                          <p class="text-xs opacity-80">{file.size ? `${Math.round(file.size / 1024)} KB` : ''}</p>
                        </div>
                      </div>
                    )}
                  </For>
                </div>
              </Show>

              <Show when={imageFiles().length === 0}>
                <div class="text-center py-8">
                  <ImageIcon size={48} class="mx-auto text-gray-400 mb-4" />
                  <p class="text-gray-500 dark:text-gray-400">暂无图片文件</p>
                  <p class="text-sm text-gray-400 dark:text-gray-500 mt-2">
                    请先上传图片文件到当前文献
                  </p>
                </div>
              </Show>
            </div>

            {/* 模态框底部 */}
            <div class="flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
              <p class="text-sm text-gray-500 dark:text-gray-400">
                点击图片即可插入到编辑器中
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCloseImagePicker}
              >
                取消
              </Button>
            </div>
          </div>
        </div>
      </Show>
    </div>
  );
};

export default MarkdownEditor;
