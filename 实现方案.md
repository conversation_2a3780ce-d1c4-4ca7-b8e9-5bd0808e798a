# Lite Papers UI 前端实现方案

## 技术栈
- **框架**: SolidJS 1.9.5
- **构建**: Vite 6.0.0 
- **样式**: Tailwind CSS
- **HTTP**: Axios
- **路由**: @solidjs/router
- **状态**: solid-js/store
- **图标**: Lucide Icons

## 项目配置
- **前端端口**: 5173 (Vite默认)
- **后端API**: http://127.0.0.1:3000
- **代理配置**: Vite proxy转发API请求

## 核心页面设计

### 1. 布局结构 (单页面应用)
```
┌─────────────────────────────────────────────────────┐
│ Header (搜索栏 + 操作按钮)                            │
├─────────────┬───────────────────────┬───────────────┤
│ Sidebar     │ Main Content          │ Detail Drawer │
│ - 文件夹树   │ - 文献列表            │ - 文献详情     │
│ - 文件夹管理 │ - 搜索结果            │ - 文件管理     │
│ - 快捷操作   │                      │ - 编辑表单     │
└─────────────┴───────────────────────┴───────────────┘
```

### 2. 页面路由 (简化)
- `/` - 主页面 (侧边栏 + 文献列表 + 右侧抽屉)

## UI设计原则

### 现代化设计风格
- **极简主义**: 干净的界面，减少视觉噪音
- **卡片布局**: 使用阴影和圆角的现代卡片设计
- **响应式**: 支持桌面、平板、手机
- **深色模式**: 可选的深色主题

### 色彩方案
- **主色**: 蓝色系 (专业、学术)
- **辅助色**: 灰色系 (中性、现代)
- **强调色**: 绿色 (成功)、红色 (警告)
- **背景**: 浅灰/白色 (简洁)

### 交互体验
- **微动画**: 悬停、点击的流畅过渡
- **抽屉动画**: 右侧抽屉滑入滑出，支持宽度调节
- **视图切换**: 平滑的视图模式切换动画
- **加载状态**: 骨架屏、进度条
- **反馈机制**: Toast通知、状态提示
- **快捷键**: 支持键盘操作 (ESC关闭抽屉、Ctrl+A全选等)

## 核心功能模块

### 1. 文献管理
- **视图切换**: 卡片视图/表格视图/列表视图切换
- **快速操作**: 编辑、删除、移动文献
- **批量操作**: 多选文献、批量删除/移动/标记
- **排序筛选**: 多维度排序和筛选
- **选择模式**: 支持单选、多选、全选操作

### 2. 搜索系统
- **实时搜索**: 顶部搜索栏，实时过滤文献列表
- **高级筛选**: 侧边栏展开高级筛选选项
- **搜索建议**: 自动补全和历史记录
- **结果高亮**: 关键词高亮显示

### 3. 文件管理
- **拖拽上传**: 支持多文件拖拽上传
- **文件下载**: 支持单个/批量文件下载
- **进度显示**: 上传/下载进度条
- **文件分类**: Origin/Note/Image标签分类
- **文件操作**: 重命名、删除、移动文件

### 4. 文件夹系统 (侧边栏集成)
- **树形结构**: 侧边栏显示可折叠的文件夹树
- **拖拽移动**: 文献在文件夹间移动
- **右键菜单**: 创建、重命名、删除文件夹
- **选中状态**: 高亮当前选中文件夹，主区域显示对应文献

## 组件架构

### 基础组件 (components/ui/)
- Button, Input, ResizableDrawer, Dropdown
- Card, Badge, Avatar, Tooltip, Checkbox
- Table, Pagination, Loading, ViewSwitcher
- FileUpload, SearchBox, BatchActions

### 布局组件 (components/layout/)
- AppLayout, Header, Sidebar, MainContent, DetailDrawer

### 功能组件 (components/features/)
- PaperCard, PaperList, PaperDetailDrawer
- SearchBar, FileManager, FolderTree
- PaperForm, AdvancedFilters

## 状态管理

### 全局状态 (stores/)
- **papers**: 文献数据缓存、当前显示列表、选中状态
- **folders**: 文件夹树结构和当前选中文件夹
- **search**: 搜索关键词和筛选条件
- **ui**: 主题、侧边栏状态、抽屉状态、视图模式、抽屉宽度等
- **selection**: 多选状态、批量操作模式

### 数据流
```
API Service → Store → Component → UI
     ↑                    ↓
   Axios              Solid Signals
```

## API服务层

### 服务模块 (services/)
- **paperService**: 文献CRUD操作
- **fileService**: 文件上传/下载
- **folderService**: 文件夹管理
- **searchService**: 搜索功能
- **statsService**: 统计数据

### 请求拦截
- 统一错误处理
- 加载状态管理
- 请求/响应日志

## 开发计划

### Phase 1: 基础架构
1. 项目配置 (Tailwind, Axios, Router)
2. 基础组件开发
3. 布局和路由设置

### Phase 2: 核心功能
1. 文献列表和详情页
2. 搜索功能实现
3. 文件上传/预览

### Phase 3: 高级功能
1. 文件夹管理
2. 批量操作
3. 统计仪表板

### Phase 4: 优化完善
1. 性能优化
2. 用户体验优化
3. 响应式适配

## 核心功能特性

### 1. 可调节抽屉
- **拖拽调节**: 抽屉边缘可拖拽调节宽度
- **记忆宽度**: 保存用户偏好的抽屉宽度
- **最小/最大限制**: 设置合理的宽度范围
- **响应式适配**: 移动端自动全屏显示

### 2. 视图模式切换
- **卡片视图**: 大图标、标题、作者、年份
- **表格视图**: 紧凑的表格形式，显示更多信息
- **列表视图**: 介于卡片和表格之间的列表形式
- **视图记忆**: 保存用户偏好的视图模式

### 3. 批量操作系统
- **多选模式**: 点击复选框进入多选模式
- **全选功能**: 支持当前页全选、全部文献全选
- **批量操作栏**: 选中文献后显示批量操作工具栏
- **操作类型**: 批量删除、移动到文件夹、导出等

## 技术要点

### SolidJS特性利用
- 细粒度响应式更新
- 组件懒加载
- 错误边界处理
- Signal-based状态管理

### 性能优化
- 虚拟滚动 (大列表)
- 组件代码分割
- API请求缓存和去重
- 防抖搜索

### 用户体验
- 离线提示
- 网络状态检测
- 操作确认对话框
- 快捷键支持
- 拖拽交互

## 部署配置
- 静态资源优化
- 路由History模式
- 环境变量配置
- 构建产物分析
