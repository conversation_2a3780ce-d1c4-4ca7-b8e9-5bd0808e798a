/**
 * 简单的PDF功能测试工具
 * 用于验证重构后的PDF文本选择和注释功能
 */

export const testPdfFunctionality = () => {
  console.log('🔍 开始测试PDF功能...');
  
  // 检查PDF文本层
  const textLayer = document.querySelector('.textLayer');
  if (textLayer) {
    const textElements = textLayer.querySelectorAll('span');
    console.log(`✅ 找到PDF文本层，包含 ${textElements.length} 个文本元素`);
    
    // 测试文本选择
    if (textElements.length > 0) {
      try {
        const selection = window.getSelection();
        selection.removeAllRanges();
        
        const range = document.createRange();
        range.selectNodeContents(textElements[0]);
        selection.addRange(range);
        
        const selectedText = selection.toString();
        if (selectedText.length > 0) {
          console.log(`✅ 文本选择功能正常，选中文本: "${selectedText}"`);
        } else {
          console.log('⚠️ 文本选择功能可能有问题');
        }
        
        selection.removeAllRanges();
      } catch (error) {
        console.error('❌ 文本选择测试失败:', error);
      }
    }
  } else {
    console.log('⚠️ 未找到PDF文本层，请确保PDF已完全加载');
  }
  
  // 检查注释层
  const annotationLayer = document.querySelector('.annotationLayer');
  if (annotationLayer) {
    console.log('✅ 找到PDF注释层');
  } else {
    console.log('⚠️ 未找到PDF注释层');
  }
  
  // 检查PDF画布
  const canvas = document.querySelector('.pdfViewer canvas');
  if (canvas) {
    console.log('✅ 找到PDF画布');
  } else {
    console.log('⚠️ 未找到PDF画布');
  }
  
  console.log('🔍 PDF功能测试完成');
};

/**
 * 测试文献列表滚动功能
 */
export const testScrolling = () => {
  console.log('🔍 测试文献列表滚动功能...');

  const mainContent = document.querySelector('main');
  const scrollContainer = document.querySelector('main .overflow-y-auto');
  const paperGrid = document.querySelector('main .overflow-y-auto > div');

  if (!mainContent || !scrollContainer || !paperGrid) {
    console.log('⚠️ 未找到必要的DOM元素');
    console.log('mainContent:', !!mainContent);
    console.log('scrollContainer:', !!scrollContainer);
    console.log('paperGrid:', !!paperGrid);
    return;
  }

  const mainRect = mainContent.getBoundingClientRect();
  const scrollRect = scrollContainer.getBoundingClientRect();
  const gridRect = paperGrid.getBoundingClientRect();

  console.log('📏 布局信息:');
  console.log('主容器高度:', mainRect.height);
  console.log('滚动容器高度:', scrollRect.height);
  console.log('内容高度:', gridRect.height);
  console.log('是否需要滚动:', gridRect.height > scrollRect.height);
  console.log('滚动容器样式:', {
    overflow: getComputedStyle(scrollContainer).overflow,
    overflowY: getComputedStyle(scrollContainer).overflowY,
    height: getComputedStyle(scrollContainer).height,
    maxHeight: getComputedStyle(scrollContainer).maxHeight
  });

  // 测试滚动功能
  if (gridRect.height > scrollRect.height) {
    console.log('🔄 测试滚动功能...');
    const originalScrollTop = scrollContainer.scrollTop;

    // 滚动到底部
    scrollContainer.scrollTop = scrollContainer.scrollHeight;
    setTimeout(() => {
      if (scrollContainer.scrollTop > originalScrollTop) {
        console.log('✅ 滚动功能正常');
      } else {
        console.log('❌ 滚动功能异常');
      }

      // 恢复原始位置
      scrollContainer.scrollTop = originalScrollTop;
    }, 100);
  } else {
    console.log('ℹ️ 内容高度不足，无需滚动');
  }
};

/**
 * 测试分页组件功能
 */
export const testPagination = () => {
  console.log('🔍 测试分页组件功能...');

  const pagination = document.querySelector('[class*="Pagination"]') ||
                    document.querySelector('.flex.items-center.justify-between');

  if (!pagination) {
    console.log('⚠️ 未找到分页组件');
    return;
  }

  // 检查页码按钮
  const pageButtons = pagination.querySelectorAll('button[title*="第"]');
  const ellipsis = pagination.querySelectorAll('span:has(svg)');
  const selectDropdown = pagination.querySelector('select') ||
                        pagination.querySelector('[class*="Select"]');

  console.log('📊 分页组件信息:');
  console.log('页码按钮数量:', pageButtons.length);
  console.log('省略号数量:', ellipsis.length);
  console.log('页面大小选择器:', !!selectDropdown);

  // 检查页码显示逻辑
  const pageNumbers = Array.from(pageButtons).map(btn => {
    const match = btn.title.match(/第 (\d+) 页/);
    return match ? parseInt(match[1]) : null;
  }).filter(Boolean);

  console.log('显示的页码:', pageNumbers);

  if (pageNumbers.length > 7) {
    console.log('⚠️ 页码显示过多，应该使用省略号');
  } else if (pageNumbers.length <= 7 && ellipsis.length === 0) {
    console.log('✅ 页码显示正常（总数≤7，无省略号）');
  } else if (pageNumbers.length <= 7 && ellipsis.length > 0) {
    console.log('✅ 页码显示正常（使用省略号）');
  }

  // 测试页面大小选择器方向
  if (selectDropdown) {
    console.log('🔄 测试页面大小选择器...');
    selectDropdown.click();

    setTimeout(() => {
      const dropdown = document.querySelector('[class*="absolute"][class*="z-50"]');
      if (dropdown) {
        const rect = dropdown.getBoundingClientRect();
        const selectRect = selectDropdown.getBoundingClientRect();

        if (rect.bottom < selectRect.top) {
          console.log('✅ 选择器向上弹出');
        } else if (rect.top > selectRect.bottom) {
          console.log('✅ 选择器向下弹出');
        } else {
          console.log('⚠️ 选择器位置异常');
        }

        // 关闭下拉框
        document.body.click();
      } else {
        console.log('⚠️ 未找到下拉选项');
      }
    }, 100);
  }
};

// 在开发环境下挂载到window对象
if (import.meta.env.DEV) {
  window.testPdfFunctionality = testPdfFunctionality;
  window.testScrolling = testScrolling;
  window.testPagination = testPagination;
  console.log('💡 在控制台输入以下命令来测试功能：');
  console.log('   - testPdfFunctionality() 测试PDF文本选择功能');
  console.log('   - testScrolling() 测试文献列表滚动功能');
  console.log('   - testPagination() 测试分页组件功能');
  console.log('🔧 分页优化说明：');
  console.log('   1. 优化页码显示逻辑，页数过多时使用省略号');
  console.log('   2. 页面大小选择器向上弹出，避免被遮挡');
  console.log('   3. 智能页码算法，确保当前页始终可见');
}
