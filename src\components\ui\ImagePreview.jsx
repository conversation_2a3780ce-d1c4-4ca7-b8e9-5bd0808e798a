import { createSignal, createMemo, onMount, onCleanup, Show, splitProps } from 'solid-js';
import { X, ZoomIn, ZoomOut, RotateCw, RotateCcw, Maximize, ChevronLeft, ChevronRight, Download, RotateCcw as Reset } from 'lucide-solid';

const ImagePreview = (props) => {
  const [local, others] = splitProps(props, [
    'open',
    'onClose',
    'images',
    'currentIndex',
    'onIndexChange',
    'title',
    'showDownload',
    'onDownload',
    'class'
  ]);

  // 图片状态
  const [scale, setScale] = createSignal(1);
  const [rotation, setRotation] = createSignal(0);
  const [position, setPosition] = createSignal({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = createSignal(false);
  const [dragStart, setDragStart] = createSignal({ x: 0, y: 0 });
  const [isLoading, setIsLoading] = createSignal(true);
  const [hasError, setHasError] = createSignal(false);
  const [isFullscreen, setIsFullscreen] = createSignal(false);

  let imageRef;
  let containerRef;

  // 当前图片信息
  const currentImage = createMemo(() => {
    const images = local.images || [];
    const index = local.currentIndex || 0;
    return images[index];
  });

  // 是否有多张图片
  const hasMultipleImages = createMemo(() => {
    return (local.images || []).length > 1;
  });

  // 重置图片状态
  const resetImageState = () => {
    setScale(1);
    setRotation(0);
    setPosition({ x: 0, y: 0 });
    setIsLoading(true);
    setHasError(false);
  };

  // 图片加载完成
  const handleImageLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  // 图片加载错误
  const handleImageError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  // 缩放操作
  const zoomIn = () => {
    setScale(prev => Math.min(prev * 1.2, 5));
  };

  const zoomOut = () => {
    setScale(prev => Math.max(prev / 1.2, 0.1));
  };

  // 旋转操作
  const rotateClockwise = () => {
    setRotation(prev => (prev + 90) % 360);
  };

  const rotateCounterClockwise = () => {
    setRotation(prev => (prev - 90 + 360) % 360);
  };

  // 重置所有变换
  const resetTransform = () => {
    setScale(1);
    setRotation(0);
    setPosition({ x: 0, y: 0 });
  };

  // 切换全屏
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      containerRef?.requestFullscreen?.();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen?.();
      setIsFullscreen(false);
    }
  };

  // 上一张图片
  const previousImage = () => {
    if (!hasMultipleImages()) return;
    const images = local.images || [];
    const currentIdx = local.currentIndex || 0;
    const newIndex = currentIdx > 0 ? currentIdx - 1 : images.length - 1;
    local.onIndexChange?.(newIndex);
    resetImageState();
  };

  // 下一张图片
  const nextImage = () => {
    if (!hasMultipleImages()) return;
    const images = local.images || [];
    const currentIdx = local.currentIndex || 0;
    const newIndex = currentIdx < images.length - 1 ? currentIdx + 1 : 0;
    local.onIndexChange?.(newIndex);
    resetImageState();
  };

  // 鼠标拖拽
  const handleMouseDown = (e) => {
    if (scale() <= 1) return;
    setIsDragging(true);
    setDragStart({
      x: e.clientX - position().x,
      y: e.clientY - position().y
    });
  };

  const handleMouseMove = (e) => {
    if (!isDragging()) return;
    setPosition({
      x: e.clientX - dragStart().x,
      y: e.clientY - dragStart().y
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 鼠标滚轮缩放
  const handleWheel = (e) => {
    e.preventDefault();
    if (e.deltaY < 0) {
      zoomIn();
    } else {
      zoomOut();
    }
  };

  // 键盘事件
  const handleKeyDown = (e) => {
    if (!local.open) return;

    switch (e.key) {
      case 'Escape':
        local.onClose?.();
        break;
      case 'ArrowLeft':
        previousImage();
        break;
      case 'ArrowRight':
        nextImage();
        break;
      case '+':
      case '=':
        zoomIn();
        break;
      case '-':
        zoomOut();
        break;
      case '0':
        resetTransform();
        break;
      case 'r':
      case 'R':
        rotateClockwise();
        break;
      case 'f':
      case 'F':
        toggleFullscreen();
        break;
    }
  };

  // 下载图片
  const handleDownload = () => {
    const image = currentImage();
    if (image && local.onDownload) {
      local.onDownload(image);
    } else if (image?.src) {
      // 默认下载逻辑
      const link = document.createElement('a');
      link.href = image.src;
      link.download = image.name || 'image';
      link.click();
    }
  };

  // 监听全屏变化
  const handleFullscreenChange = () => {
    setIsFullscreen(!!document.fullscreenElement);
  };

  onMount(() => {
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('fullscreenchange', handleFullscreenChange);
  });

  onCleanup(() => {
    document.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('fullscreenchange', handleFullscreenChange);
  });

  // 当图片变化时重置状态
  createMemo(() => {
    if (currentImage()) {
      resetImageState();
    }
  });

  const imageStyle = () => ({
    transform: `translate(${position().x}px, ${position().y}px) scale(${scale()}) rotate(${rotation()}deg)`,
    cursor: scale() > 1 ? (isDragging() ? 'grabbing' : 'grab') : 'default',
    transition: isDragging() ? 'none' : 'transform 0.2s ease-out'
  });

  return (
    <Show when={local.open}>
      <div
        ref={containerRef}
        class={`fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center ${local.class || ''}`}
        {...others}
      >
        {/* 工具栏 */}
        <div class="absolute top-4 left-1/2 transform -translate-x-1/2 z-10 flex items-center space-x-2 bg-black bg-opacity-50 rounded-lg px-4 py-2">
          {/* 图片计数 */}
          {hasMultipleImages() && (
            <span class="text-white text-sm">
              {(local.currentIndex || 0) + 1} / {(local.images || []).length}
            </span>
          )}
          
          {/* 标题 */}
          {local.title && (
            <span class="text-white text-sm font-medium">{local.title}</span>
          )}
        </div>

        {/* 操作按钮 */}
        <div class="absolute top-4 right-4 z-10 flex items-center space-x-2">
          {/* 缩放按钮 */}
          <button
            onClick={zoomOut}
            class="p-2 bg-black bg-opacity-50 text-white rounded-lg hover:bg-opacity-70 transition-colors"
            title="缩小 (-)"
          >
            <ZoomOut size={20} />
          </button>
          
          <button
            onClick={zoomIn}
            class="p-2 bg-black bg-opacity-50 text-white rounded-lg hover:bg-opacity-70 transition-colors"
            title="放大 (+)"
          >
            <ZoomIn size={20} />
          </button>

          {/* 旋转按钮 */}
          <button
            onClick={rotateCounterClockwise}
            class="p-2 bg-black bg-opacity-50 text-white rounded-lg hover:bg-opacity-70 transition-colors"
            title="逆时针旋转"
          >
            <RotateCcw size={20} />
          </button>
          
          <button
            onClick={rotateClockwise}
            class="p-2 bg-black bg-opacity-50 text-white rounded-lg hover:bg-opacity-70 transition-colors"
            title="顺时针旋转 (R)"
          >
            <RotateCw size={20} />
          </button>

          {/* 重置按钮 */}
          <button
            onClick={resetTransform}
            class="p-2 bg-black bg-opacity-50 text-white rounded-lg hover:bg-opacity-70 transition-colors"
            title="重置 (0)"
          >
            <Reset size={20} />
          </button>

          {/* 全屏按钮 */}
          <button
            onClick={toggleFullscreen}
            class="p-2 bg-black bg-opacity-50 text-white rounded-lg hover:bg-opacity-70 transition-colors"
            title="全屏 (F)"
          >
            <Maximize size={20} />
          </button>

          {/* 下载按钮 */}
          {local.showDownload && (
            <button
              onClick={handleDownload}
              class="p-2 bg-black bg-opacity-50 text-white rounded-lg hover:bg-opacity-70 transition-colors"
              title="下载图片"
            >
              <Download size={20} />
            </button>
          )}

          {/* 关闭按钮 */}
          <button
            onClick={local.onClose}
            class="p-2 bg-black bg-opacity-50 text-white rounded-lg hover:bg-opacity-70 transition-colors"
            title="关闭 (ESC)"
          >
            <X size={20} />
          </button>
        </div>

        {/* 图片导航 */}
        {hasMultipleImages() && (
          <>
            <button
              onClick={previousImage}
              class="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-colors"
              title="上一张 (←)"
            >
              <ChevronLeft size={24} />
            </button>
            
            <button
              onClick={nextImage}
              class="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-colors"
              title="下一张 (→)"
            >
              <ChevronRight size={24} />
            </button>
          </>
        )}

        {/* 图片容器 */}
        <div
          class="flex items-center justify-center w-full h-full overflow-hidden"
          onWheel={handleWheel}
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              local.onClose?.();
            }
          }}
        >
          {/* 加载状态 */}
          <Show when={isLoading()}>
            <div class="text-white text-center">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
              <p>加载中...</p>
            </div>
          </Show>

          {/* 错误状态 */}
          <Show when={hasError()}>
            <div class="text-white text-center">
              <p class="text-lg mb-2">图片加载失败</p>
              <p class="text-sm opacity-70">请检查图片链接是否有效</p>
            </div>
          </Show>

          {/* 图片 */}
          <Show when={currentImage() && !hasError()}>
            <img
              ref={imageRef}
              src={currentImage()?.src}
              alt={currentImage()?.alt || currentImage()?.name || '图片预览'}
              class="max-w-full max-h-full object-contain select-none"
              style={imageStyle()}
              onLoad={handleImageLoad}
              onError={handleImageError}
              onMouseDown={handleMouseDown}
              draggable={false}
            />
          </Show>
        </div>

        {/* 底部信息栏 */}
        <Show when={currentImage() && !isLoading() && !hasError()}>
          <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10 bg-black bg-opacity-50 rounded-lg px-4 py-2">
            <div class="text-white text-sm text-center">
              <div class="flex items-center space-x-4">
                {currentImage()?.name && (
                  <span>{currentImage().name}</span>
                )}
                <span>{Math.round(scale() * 100)}%</span>
                {rotation() !== 0 && (
                  <span>{rotation()}°</span>
                )}
              </div>
            </div>
          </div>
        </Show>
      </div>
    </Show>
  );
};

export default ImagePreview;
