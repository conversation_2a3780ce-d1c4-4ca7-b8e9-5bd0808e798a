import { FolderTree } from '../ui';

/**
 * 文件夹选择器组件 - 基于通用 FolderTree 组件的封装
 * 主要用于选择文件夹的场景，如移动文献、创建文献等
 */
const FolderSelector = (props) => {
  return (
    <FolderTree
      folderTree={props.folderTree}
      selectedFolder={props.selectedFolder}
      onSelect={props.onSelect}
      showAllPapersOption={false}
      showPaperCount={true}
      variant="selector"
      expandable={true}
      maxDepth={5}
    />
  );
};



export default FolderSelector;
