import { createSignal, createEffect, onCleanup, splitProps } from 'solid-js';
import { Search, X, Clock } from 'lucide-solid';
import Loading from './Loading';
import Input from './Input';

const SearchBox = (props) => {
  const [local, others] = splitProps(props, [
    'value',
    'placeholder',
    'onSearch',
    'onChange',
    'onClear',
    'debounceMs',
    'showHistory',
    'historyKey',
    'maxHistory',
    'size',
    'disabled',
    'loading',
    'showShortcut',
    'shortcutText',
    'class'
  ]);

  const [inputValue, setInputValue] = createSignal(local.value || '');
  const [searchHistory, setSearchHistory] = createSignal([]);
  const [showHistoryDropdown, setShowHistoryDropdown] = createSignal(false);


  let searchTimeout;
  let inputRef;
  let dropdownRef;

  const debounceMs = local.debounceMs || 300;
  const maxHistory = local.maxHistory || 10;
  const historyKey = local.historyKey || 'search_history';
  const size = local.size || 'md';



  // 加载搜索历史
  const loadSearchHistory = () => {
    if (local.showHistory) {
      try {
        const history = JSON.parse(localStorage.getItem(historyKey) || '[]');
        setSearchHistory(history);
      } catch (e) {
        setSearchHistory([]);
      }
    }
  };

  // 保存搜索历史
  const saveSearchHistory = (query) => {
    if (!local.showHistory || !query.trim()) return;

    const history = searchHistory();
    const newHistory = [query, ...history.filter(item => item !== query)].slice(0, maxHistory);
    setSearchHistory(newHistory);
    
    try {
      localStorage.setItem(historyKey, JSON.stringify(newHistory));
    } catch (e) {
      console.warn('Failed to save search history:', e);
    }
  };

  // 防抖搜索
  const debouncedSearch = (value) => {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      if (local.onSearch) {
        local.onSearch(value);
      }
    }, debounceMs);
  };

  // 处理输入变化
  const handleInputChange = (e) => {
    const value = e.target.value;
    setInputValue(value);

    // 调用 onChange 回调
    if (local.onChange) {
      local.onChange(value);
    }

    // 防抖搜索
    debouncedSearch(value);
  };

  // 处理搜索
  const handleSearch = (query = inputValue()) => {
    if (local.onSearch) {
      local.onSearch(query);
    }
    saveSearchHistory(query);
    setShowHistoryDropdown(false);
  };

  // 处理清空
  const handleClear = () => {
    setInputValue('');
    setShowHistoryDropdown(false);
    
    if (local.onChange) {
      local.onChange('');
    }
    
    if (local.onClear) {
      local.onClear();
    }
    
    if (local.onSearch) {
      local.onSearch('');
    }
  };

  // 处理焦点
  const handleFocus = () => {
    if (local.showHistory && searchHistory().length > 0) {
      setShowHistoryDropdown(true);
    }
  };

  const handleBlur = () => {
    // 延迟关闭下拉框，以便点击历史项
    setTimeout(() => {
      setShowHistoryDropdown(false);
    }, 200);
  };

  // 处理键盘事件
  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch();
    } else if (e.key === 'Escape') {
      setShowHistoryDropdown(false);
      inputRef?.blur();
    }
  };

  // 选择历史项
  const selectHistoryItem = (item) => {
    setInputValue(item);
    handleSearch(item);
  };

  // 删除历史项
  const removeHistoryItem = (item, e) => {
    e.stopPropagation();
    const newHistory = searchHistory().filter(h => h !== item);
    setSearchHistory(newHistory);
    
    try {
      localStorage.setItem(historyKey, JSON.stringify(newHistory));
    } catch (e) {
      console.warn('Failed to update search history:', e);
    }
  };

  // 监听外部 value 变化
  createEffect(() => {
    if (local.value !== undefined && local.value !== inputValue()) {
      setInputValue(local.value);
    }
  });

  // 组件挂载时加载历史
  loadSearchHistory();

  // 清理定时器
  onCleanup(() => {
    clearTimeout(searchTimeout);
  });

  return (
    <div class={`relative ${local.class || ''}`}>
      <div class="relative">
        {/* 使用 Input 组件 */}
        <Input
          ref={inputRef}
          value={inputValue()}
          onInput={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          placeholder={local.placeholder || '搜索...'}
          disabled={local.disabled}
          size={size}
          leftIcon={local.loading ? <Loading size="sm" /> : <Search size={16} />}
          rightElement={
            <div class="flex items-center space-x-2">
              {/* 快捷键显示 */}
              {local.showShortcut && local.shortcutText && !inputValue() && (
                <kbd class="hidden sm:inline-flex items-center px-2 py-1 text-xs font-medium text-gray-500 bg-gray-100 border border-gray-200 rounded">
                  {local.shortcutText}
                </kbd>
              )}

              {/* 清空按钮 */}
              {inputValue() && (
                <button
                  onClick={handleClear}
                  class="text-gray-400 hover:text-gray-600 transition-colors p-1"
                >
                  <X size={16} />
                </button>
              )}
            </div>
          }
          {...others}
        />
      </div>

      {/* 搜索历史下拉框 */}
      {showHistoryDropdown() && searchHistory().length > 0 && (
        <div 
          ref={dropdownRef}
          class="absolute top-full left-0 right-0 mt-1 bg-theme-elevated border border-theme-primary rounded-lg shadow-theme-lg z-50 max-h-60 overflow-y-auto"
        >
          <div class="py-1">
            <div class="px-3 py-2 text-xs font-medium text-theme-muted border-b border-theme-muted">
              搜索历史
            </div>
            {searchHistory().map((item) => (
              <div
                key={item}
                class="flex items-center justify-between px-3 py-2 hover:bg-theme-muted cursor-pointer group"
                onClick={() => selectHistoryItem(item)}
              >
                <div class="flex items-center space-x-2 flex-1 min-w-0">
                  <Clock size={14} class="text-theme-muted flex-shrink-0" />
                  <span class="text-sm text-theme-primary truncate">{item}</span>
                </div>
                <button
                  onClick={(e) => removeHistoryItem(item, e)}
                  class="opacity-0 group-hover:opacity-100 p-1 text-theme-muted hover:text-theme-error transition-all"
                >
                  <X size={12} />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchBox;
