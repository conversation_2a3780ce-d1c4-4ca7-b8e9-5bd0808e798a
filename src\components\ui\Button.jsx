import { Show, splitProps } from 'solid-js';
import Loading from './Loading';

const Button = (props) => {
  const [local, others] = splitProps(props, [
    'children',
    'variant',
    'size',
    'disabled',
    'loading',
    'class'
  ]);

  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

  const variants = {
    primary: 'btn-primary-theme focus:ring-theme-accent',
    secondary: 'btn-secondary-theme focus:ring-theme-accent',
    outline: 'btn-outline-theme border focus:ring-theme-accent',
    ghost: 'text-theme-secondary hover:bg-theme-muted hover:text-theme-primary focus:ring-theme-accent',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
    success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500'
  };

  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg'
  };

  return (
    <button
      class={`${baseClasses} ${variants[local.variant || 'primary']} ${sizes[local.size || 'md']} ${local.class || ''}`}
      disabled={local.disabled || local.loading}
      {...others}
    >
      <Show when={local.loading}>
        <Loading size="sm" class="text-current mr-2" />
      </Show>
      {local.children}
    </button>
  );
};

export default Button;
