import { createSignal, Show, onMount, onCleanup, splitProps } from 'solid-js';

const Tooltip = (props) => {
  const [local, others] = splitProps(props, [
    'children',
    'content',
    'placement',
    'trigger',
    'disabled',
    'delay',
    'class',
    'contentClass'
  ]);

  const [visible, setVisible] = createSignal(false);
  const [position, setPosition] = createSignal({ top: 0, left: 0 });

  let triggerRef;
  let tooltipRef;
  let showTimeout;
  let hideTimeout;

  const placement = local.placement || 'bottom';
  const trigger = local.trigger || 'hover';
  const delay = local.delay || 100;

  const placements = {
    top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
    'top-start': 'bottom-full left-0 mb-2',
    'top-end': 'bottom-full right-0 mb-2',
    bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
    'bottom-start': 'top-full left-0 mt-2',
    'bottom-end': 'top-full right-0 mt-2',
    left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
    right: 'left-full top-1/2 transform -translate-y-1/2 ml-2'
  };

  const arrows = {
    top: 'top-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-b-transparent border-t-gray-900',
    'top-start': 'top-full left-2 border-l-transparent border-r-transparent border-b-transparent border-t-gray-900',
    'top-end': 'top-full right-2 border-l-transparent border-r-transparent border-b-transparent border-t-gray-900',
    bottom: 'bottom-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-t-transparent border-b-gray-900',
    'bottom-start': 'bottom-full left-2 border-l-transparent border-r-transparent border-t-transparent border-b-gray-900',
    'bottom-end': 'bottom-full right-2 border-l-transparent border-r-transparent border-t-transparent border-b-gray-900',
    left: 'left-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-r-transparent border-l-gray-900',
    right: 'right-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-l-transparent border-r-gray-900'
  };

  const showTooltip = () => {
    if (local.disabled) return;
    
    clearTimeout(hideTimeout);
    showTimeout = setTimeout(() => {
      setVisible(true);
    }, delay);
  };

  const hideTooltip = () => {
    clearTimeout(showTimeout);
    hideTimeout = setTimeout(() => {
      setVisible(false);
    }, delay);
  };

  const handleMouseEnter = () => {
    if (trigger === 'hover') {
      showTooltip();
    }
  };

  const handleMouseLeave = () => {
    if (trigger === 'hover') {
      hideTooltip();
    }
  };

  const handleClick = () => {
    if (trigger === 'click') {
      if (visible()) {
        hideTooltip();
      } else {
        showTooltip();
      }
    }
  };

  const handleFocus = () => {
    if (trigger === 'focus') {
      showTooltip();
    }
  };

  const handleBlur = () => {
    if (trigger === 'focus') {
      hideTooltip();
    }
  };

  // 点击外部关闭（仅在 click 触发模式下）
  const handleClickOutside = (event) => {
    if (trigger === 'click' && visible() && 
        triggerRef && !triggerRef.contains(event.target) &&
        tooltipRef && !tooltipRef.contains(event.target)) {
      hideTooltip();
    }
  };

  onMount(() => {
    if (trigger === 'click') {
      document.addEventListener('click', handleClickOutside);
    }
  });

  onCleanup(() => {
    clearTimeout(showTimeout);
    clearTimeout(hideTimeout);
    if (trigger === 'click') {
      document.removeEventListener('click', handleClickOutside);
    }
  });

  return (
    <div 
      ref={triggerRef}
      class={`relative inline-block ${local.class || ''}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
      onFocus={handleFocus}
      onBlur={handleBlur}
      {...others}
    >
      {/* 触发元素 */}
      {local.children}

      {/* Tooltip 内容 */}
      <Show when={visible() && local.content}>
        <div
          ref={tooltipRef}
          class={`
            absolute z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-lg shadow-lg
            whitespace-nowrap pointer-events-none
            ${placements[placement]}
            ${local.contentClass || ''}
          `}
          style={{
            animation: 'fadeIn 0.15s ease-out'
          }}
        >
          {local.content}
          
          {/* 箭头 */}
          <div 
            class={`absolute w-0 h-0 border-4 ${arrows[placement]}`}
          />
        </div>
      </Show>

      {/* CSS 动画 */}
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: scale(0.95);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
      `}</style>
    </div>
  );
};

export default Tooltip;
