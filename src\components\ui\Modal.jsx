import { Show, onMount, onCleanup, splitProps } from 'solid-js';
import { X } from 'lucide-solid';

const Modal = (props) => {
  const [local, others] = splitProps(props, [
    'children',
    'open',
    'onClose',
    'title',
    'size',
    'closable',
    'maskClosable',
    'class',
    'bodyClass'
  ]);

  let modalRef;
  let mouseDownTarget = null;
  let mouseDownPosition = null;

  const size = local.size || 'md';

  const sizes = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-full mx-4'
  };

  // ESC 键关闭
  const handleKeyDown = (e) => {
    if (e.key === 'Escape' && local.closable !== false && local.onClose) {
      local.onClose();
    }
  };

  // 鼠标按下时记录位置和目标
  const handleMouseDown = (e) => {
    mouseDownTarget = e.target;
    mouseDownPosition = { x: e.clientX, y: e.clientY };
  };

  // 点击遮罩关闭（只有在同一位置按下和释放时才关闭）
  const handleMaskClick = (e) => {
    // 检查是否是在遮罩上点击（不是在 modal 内容上）
    if (e.target !== e.currentTarget) {
      return;
    }

    // 检查是否禁用了遮罩关闭
    if (local.maskClosable === false || !local.onClose) {
      return;
    }

    // 检查是否是真正的点击（而不是拖拽）
    if (mouseDownTarget === e.target && mouseDownPosition) {
      const currentPosition = { x: e.clientX, y: e.clientY };
      const distance = Math.sqrt(
        Math.pow(currentPosition.x - mouseDownPosition.x, 2) +
        Math.pow(currentPosition.y - mouseDownPosition.y, 2)
      );

      // 如果移动距离小于 5 像素，认为是点击而不是拖拽
      if (distance < 5) {
        local.onClose();
      }
    }

    // 重置状态
    mouseDownTarget = null;
    mouseDownPosition = null;
  };

  onMount(() => {
    document.addEventListener('keydown', handleKeyDown);
  });

  onCleanup(() => {
    document.removeEventListener('keydown', handleKeyDown);
  });

  return (
    <Show when={local.open}>
      <div
        class="fixed inset-0 z-50 overflow-y-auto"
        onMouseDown={handleMouseDown}
        onClick={handleMaskClick}
      >
        {/* 遮罩层 */}
        <div
          class="fixed inset-0 transition-opacity modal-overlay-theme"
        />
        
        {/* 模态框容器 */}
        <div class="flex min-h-full items-center justify-center p-4">
          <div
            ref={modalRef}
            class={`relative modal-theme rounded-xl shadow-theme-lg w-full ${sizes[size]} ${local.class || ''}`}
            {...others}
          >
            {/* 头部 */}
            {(local.title || local.closable !== false) && (
              <div class="flex items-center justify-between p-6 border-b border-theme-primary">
                {local.title && (
                  <h3 class="text-lg font-semibold text-theme-primary">
                    {local.title}
                  </h3>
                )}
                {local.closable !== false && (
                  <button
                    onClick={local.onClose}
                    class="p-2 text-theme-muted hover:text-theme-primary hover:bg-theme-muted rounded-lg transition-colors"
                  >
                    <X size={20} />
                  </button>
                )}
              </div>
            )}
            
            {/* 内容 */}
            <div class={`p-6 ${local.bodyClass || ''}`}>
              {local.children}
            </div>
          </div>
        </div>
      </div>
    </Show>
  );
};

export default Modal;
