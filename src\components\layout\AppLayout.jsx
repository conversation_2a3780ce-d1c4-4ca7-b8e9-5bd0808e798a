import { createSignal, createEffect, onMount } from 'solid-js';
import Header from './Header';
import Sidebar from './Sidebar';
import MainContent from './MainContent';
import DetailDrawer from './DetailDrawer';
import CreatePaperModal from '../features/CreatePaperModal';
import { healthCheck, papersAPI } from '../../services/api';
import { ToastContainer } from '../ui';

const AppLayout = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = createSignal(false);
  const [drawerOpen, setDrawerOpen] = createSignal(false);
  // 计算默认抽屉宽度的函数
  const getDefaultDrawerWidth = () => {
    const screenWidth = window.innerWidth;
    if (screenWidth >= 2560) return 900;  // 超大屏幕 (4K)
    if (screenWidth >= 1920) return 800;  // 大屏幕 (1080p)
    if (screenWidth >= 1440) return 600;  // 中大屏幕
    if (screenWidth >= 1024) return 500;  // 中等屏幕
    return 400;  // 小屏幕
  };

  // 计算初始抽屉宽度
  const getInitialDrawerWidth = () => {
    const saved = localStorage.getItem('drawer-width');
    if (saved && saved !== 'NaN') {
      const parsedWidth = parseInt(saved, 10);
      // 验证解析的值是否有效
      if (!isNaN(parsedWidth) && parsedWidth >= 300 && parsedWidth <= 1200) {
        return parsedWidth;
      }
    }
    // 如果没有保存的值或值无效，使用默认值
    return getDefaultDrawerWidth();
  };

  // 从 localStorage 读取保存的抽屉宽度，根据屏幕大小计算默认值
  const [drawerWidth, setDrawerWidth] = createSignal(getInitialDrawerWidth());
  const [selectedPaper, setSelectedPaper] = createSignal(null);
  const [apiConnected, setApiConnected] = createSignal(false);
  const [globalSearchQuery, setGlobalSearchQuery] = createSignal('');
  const [showCreatePaperModal, setShowCreatePaperModal] = createSignal(false);
  // 检查是否为测试模式
  const isTestMode = () => {
    return window.location.search.includes('test=pdf');
  };

  // 监听抽屉宽度变化并保存到 localStorage
  createEffect(() => {
    const width = drawerWidth();
    // 只保存有效的宽度值
    if (!isNaN(width) && width >= 300 && width <= 1200) {
      localStorage.setItem('drawer-width', width.toString());
    }
  });

  // 检查API连接状态
  onMount(async () => {
    // 清理无效的 localStorage 值
    const saved = localStorage.getItem('drawer-width');
    if (saved === 'NaN' || saved === 'undefined' || saved === 'null') {
      localStorage.removeItem('drawer-width');
      console.log('清理了无效的抽屉宽度值:', saved);
    }

    try {
      await healthCheck();
      setApiConnected(true);
      console.log('✅ API连接成功');
    } catch (error) {
      console.error('❌ API连接失败:', error.message);
      setApiConnected(false);
    }
  });

  // 打开详情抽屉
  const openDrawer = (paper) => {
    setSelectedPaper(paper);
    setDrawerOpen(true);
  };

  // 关闭详情抽屉
  const closeDrawer = () => {
    setDrawerOpen(false);
    setSelectedPaper(null);
  };

  // 切换侧边栏
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed());
  };



  // 处理全局搜索
  const handleGlobalSearch = (query) => {
    setGlobalSearchQuery(query);
    console.log('全局搜索:', query);
  };

  // 打开添加文献对话框
  const handleAddPaper = () => {
    setShowCreatePaperModal(true);
  };

  // 关闭添加文献对话框
  const handleCloseCreatePaper = () => {
    setShowCreatePaperModal(false);
  };

  // 添加文献成功回调
  const handleCreatePaperSuccess = (newPaper) => {
    console.log('文献创建成功:', newPaper);
    // 可以在这里添加额外的处理逻辑，比如打开新创建的文献详情
  };

  // 文献更新回调（用于刷新文献数据）
  const handlePaperUpdate = async (paperId) => {
    try {
      // 重新获取文献详情
      const response = await papersAPI.getById(paperId);
      if (response.data && response.data.code === 200) {
        const updatedPaper = response.data.data;
        // 更新选中的文献数据
        setSelectedPaper(updatedPaper);
        console.log('文献数据已刷新:', updatedPaper);
      }
    } catch (error) {
      console.error('刷新文献数据失败:', error);
    }
  };

  return (
    <div class="h-screen flex flex-col bg-gray-50">

      {/* Header */}
      <Header
        onToggleSidebar={toggleSidebar}
        sidebarCollapsed={sidebarCollapsed()}
        apiConnected={apiConnected()}
        onGlobalSearch={handleGlobalSearch}
        onAddPaper={handleAddPaper}
      />

      {/* Main Layout */}
      <div class="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        <Sidebar
          collapsed={sidebarCollapsed()}
          onToggle={toggleSidebar}
        />

        {/* Main Content */}
        <MainContent
          onPaperSelect={openDrawer}
          drawerOpen={drawerOpen()}
          drawerWidth={drawerWidth()}
          globalSearchQuery={globalSearchQuery()}
        />

        {/* Detail Drawer */}
        <DetailDrawer
          open={drawerOpen()}
          onClose={closeDrawer}
          paper={selectedPaper()}
          width={drawerWidth()}
          onWidthChange={setDrawerWidth}
          onPaperUpdate={handlePaperUpdate}
        />
      </div>

      {/* 添加文献对话框 */}
      <CreatePaperModal
        open={showCreatePaperModal()}
        onClose={handleCloseCreatePaper}
        onSuccess={handleCreatePaperSuccess}
      />

      {/* Toast 容器 */}
      <ToastContainer />

    </div>

  );
};

export default AppLayout;
