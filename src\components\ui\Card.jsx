import { splitProps } from 'solid-js';

const Card = (props) => {
  const [local, others] = splitProps(props, [
    'children',
    'header',
    'footer',
    'variant',
    'padding',
    'hover',
    'class'
  ]);

  const baseClasses = 'card-theme rounded-xl border transition-all duration-200';

  const variants = {
    default: '',
    elevated: 'shadow-theme-sm',
    outlined: 'border-2',
    ghost: 'border-transparent bg-theme-muted'
  };

  const paddings = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };

  const variant = local.variant || 'default';
  const padding = local.padding || 'md';

  const hoverClasses = local.hover ? 'hover:shadow-theme-lg hover:border-theme-secondary cursor-pointer' : '';

  const classes = `${baseClasses} ${variants[variant]} ${paddings[padding]} ${hoverClasses} ${local.class || ''}`;

  return (
    <div class={classes} {...others}>
      {local.header && (
        <div class="border-b border-theme-primary pb-4 mb-4">
          {local.header}
        </div>
      )}

      <div class="flex-1">
        {local.children}
      </div>

      {local.footer && (
        <div class="border-t border-theme-primary pt-4 mt-4">
          {local.footer}
        </div>
      )}
    </div>
  );
};

export default Card;
