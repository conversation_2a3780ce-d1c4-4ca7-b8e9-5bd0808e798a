import { createContext, useContext, createSignal, createEffect, onMount } from 'solid-js';
import { papersAPI, filesAPI, foldersAPI, cacheAPI } from '../services/api';

// 创建Context
const PaperContext = createContext();

// Context Provider组件
export const PaperProvider = (props) => {
  // 文献相关状态
  const [papers, setPapers] = createSignal([]);
  const [papersLoading, setPapersLoading] = createSignal(false);
  const [foldersLoading, setFoldersLoading] = createSignal(false);
  const [error, setError] = createSignal(null);

  // 合并的loading状态（向后兼容）
  const loading = () => papersLoading() || foldersLoading();

  // 分页状态
  const [pagination, setPagination] = createSignal({
    page: 1,
    page_size: 20,
    total: 0,
    total_pages: 0,
    has_next: false,
    has_prev: false
  });

  // 搜索和筛选状态
  const [searchQuery, setSearchQuery] = createSignal('');
  const [filters, setFilters] = createSignal({
    folder: null,
    year_start: null,
    year_end: null,
    sort_by: 'created_at',
    sort_order: 'desc'
  });

  // 选择状态
  const [selectedPapers, setSelectedPapers] = createSignal(new Set());
  const [selectMode, setSelectMode] = createSignal(false);

  // 文件夹相关状态
  const [folders, setFolders] = createSignal([]);
  const [folderTree, setFolderTree] = createSignal([]);

  // 统计信息状态
  const [stats, setStats] = createSignal(null);

  // 当前选中的文献
  const [currentPaper, setCurrentPaper] = createSignal(null);

  // 构建API参数的辅助函数，排除空值
  const buildApiParams = (baseParams = {}) => {
    const params = { ...baseParams };

    // 添加筛选条件，但排除空值
    const currentFilters = filters();

    // 文件夹筛选逻辑：
    // - null: 不传递 folder 参数，显示全部文献
    // - 空字符串: 传递 folder=''，显示根目录文献
    // - 其他值: 传递具体的文件夹路径
    if (currentFilters.folder !== null) {
      params.folder = currentFilters.folder || '';
    }

    if (currentFilters.year_start) {
      params.year_start = currentFilters.year_start;
    }
    if (currentFilters.year_end) {
      params.year_end = currentFilters.year_end;
    }
    if (currentFilters.sort_by) {
      params.sort_by = currentFilters.sort_by;
    }
    if (currentFilters.sort_order) {
      params.sort_order = currentFilters.sort_order;
    }

    // 如果有搜索查询，添加到参数中
    if (searchQuery()) {
      params.q = searchQuery();
    }

    return params;
  };

  // 加载文献列表
  const loadPapers = async (options = {}) => {
    try {
      setPapersLoading(true);
      setError(null);

      const baseParams = {
        page: pagination().page,
        page_size: pagination().page_size,
        ...options
      };

      const params = buildApiParams(baseParams);

      console.log('Loading papers with params:', params);

      const response = await papersAPI.getList(params);

      if (response.data && response.data.code === 200) {
        const data = response.data.data;
        setPapers(data.papers || []);
        setPagination(data.pagination || pagination());
      }
    } catch (err) {
      console.error('Failed to load papers:', err);
      setError(err.message);
      setPapers([]);
    } finally {
      setPapersLoading(false);
    }
  };



  // 设置搜索查询
  const setSearch = async (query) => {
    setSearchQuery(query);
    setPagination(prev => ({ ...prev, page: 1 })); // 重置到第一页
    await loadPapers();
  };

  // 更新筛选条件
  const updateFilters = async (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPagination(prev => ({ ...prev, page: 1 })); // 重置到第一页
    await loadPapers();
  };

  // 切换页面
  const changePage = async (page) => {
    setPagination(prev => ({ ...prev, page }));
    await loadPapers();
  };

  // 改变每页大小
  const changePageSize = async (pageSize) => {
    setPagination(prev => ({
      ...prev,
      page_size: pageSize,
      page: 1 // 重置到第一页
    }));
    await loadPapers();
  };

  // 创建文献
  const createPaper = async (paperData) => {
    try {
      setPapersLoading(true);
      const response = await papersAPI.create(paperData);

      if (response.data && response.data.code === 200) {
        // 重新加载文献列表和文件夹树（更新文献数量）
        await Promise.all([
          loadPapers(),
          loadFolderTree()
        ]);
        return response.data.data;
      }
    } catch (err) {
      console.error('Failed to create paper:', err);
      setError(err.message);
      throw err;
    } finally {
      setPapersLoading(false);
    }
  };

  // 更新文献
  const updatePaper = async (id, paperData) => {
    try {
      setPapersLoading(true);
      const response = await papersAPI.update(id, paperData);

      if (response.data && response.data.code === 200) {
        // 更新本地状态
        setPapers(prev => prev.map(paper =>
          paper.id === id ? { ...paper, ...response.data.data } : paper
        ));

        // 如果是当前选中的文献，也更新它
        if (currentPaper()?.id === id) {
          setCurrentPaper(prev => ({ ...prev, ...response.data.data }));
        }

        // 如果更新了文件夹路径，重新加载文件夹树以更新文献数量
        if (paperData.folder_path !== undefined) {
          await loadFolderTree();
        }

        return response.data.data;
      }
    } catch (err) {
      console.error('Failed to update paper:', err);
      setError(err.message);
      throw err;
    } finally {
      setPapersLoading(false);
    }
  };

  // 删除文献
  const deletePaper = async (id) => {
    try {
      setPapersLoading(true);
      await papersAPI.delete(id);

      // 从本地状态中移除
      setPapers(prev => prev.filter(paper => paper.id !== id));

      // 如果删除的是当前选中的文献，清空选中状态
      if (currentPaper()?.id === id) {
        setCurrentPaper(null);
      }

      // 从选中列表中移除
      setSelectedPapers(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });

      // 重新加载文件夹树以更新文献数量
      await loadFolderTree();

    } catch (err) {
      console.error('Failed to delete paper:', err);
      setError(err.message);
      throw err;
    } finally {
      setPapersLoading(false);
    }
  };

  // 获取文献详情
  const getPaperById = async (id) => {
    try {
      const response = await papersAPI.getById(id);

      if (response.data && response.data.code === 200) {
        const paper = response.data.data;
        setCurrentPaper(paper);
        return paper;
      }
    } catch (err) {
      console.error('Failed to get paper:', err);
      setError(err.message);
      throw err;
    }
  };

  // 选择操作
  const togglePaperSelection = (paperId) => {
    setSelectedPapers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(paperId)) {
        newSet.delete(paperId);
      } else {
        newSet.add(paperId);
      }
      return newSet;
    });
  };

  const clearSelection = () => {
    setSelectedPapers(new Set());
    setSelectMode(false);
  };

  const selectAllPapers = () => {
    const allIds = papers().map(paper => paper.id);
    setSelectedPapers(new Set(allIds));
  };

  // 批量删除
  const deleteSelectedPapers = async () => {
    const selectedIds = Array.from(selectedPapers());

    try {
      setPapersLoading(true);

      // 并行删除所有选中的文献
      await Promise.all(selectedIds.map(id => papersAPI.delete(id)));

      // 从本地状态中移除
      setPapers(prev => prev.filter(paper => !selectedIds.includes(paper.id)));

      // 清空选择
      clearSelection();

      // 重新加载文件夹树以更新文献数量
      await loadFolderTree();

    } catch (err) {
      console.error('Failed to delete selected papers:', err);
      setError(err.message);
      throw err;
    } finally {
      setPapersLoading(false);
    }
  };

  // 加载文件夹树
  const loadFolderTree = async () => {
    try {
      setFoldersLoading(true);
      const response = await foldersAPI.getTree();

      if (response.data && response.data.code === 200) {
        let treeData = response.data.data || [];

        // 过滤掉可能的根目录项（名称包含"根目录"或"papers"的项）
        // treeData = treeData.filter(folder =>
        //   !folder.name.includes('根目录') &&
        //   !folder.name.includes('papers') &&
        //   folder.name !== 'root'
        // );

        setFolderTree(treeData);
      }
    } catch (err) {
      console.error('Failed to load folder tree:', err);
    } finally {
      setFoldersLoading(false);
    }
  };

  // 创建文件夹
  const createFolder = async (name, parentPath = '') => {
    try {
      setFoldersLoading(true);

      const folderData = {
        name: name.trim(),
        parent_path: parentPath || ''
      };

      console.log('Creating folder:', folderData);

      const response = await foldersAPI.create(folderData);

      if (response.data && response.data.code === 200) {
        // 重新加载文件夹树以显示新创建的文件夹
        await loadFolderTree();
        return response.data.data;
      }
    } catch (err) {
      console.error('Failed to create folder:', err);
      setError(err.message);
      throw err;
    } finally {
      setFoldersLoading(false);
    }
  };

  // 加载统计信息
  const loadStats = async () => {
    try {
      const response = await papersAPI.getStats();

      if (response.data && response.data.code === 200) {
        setStats(response.data.data);
      }
    } catch (err) {
      console.error('Failed to load stats:', err);
    }
  };

  // 重新加载缓存
  const reloadCache = async () => {
    try {
      await cacheAPI.reload();
      // 重新加载数据
      await Promise.all([
        loadPapers(),
        loadFolderTree(),
        loadStats()
      ]);
    } catch (err) {
      console.error('Failed to reload cache:', err);
      setError(err.message);
    }
  };

  // 文件管理相关方法
  const uploadFile = async (paperId, file, type = 'note') => {
    try {
      const response = await filesAPI.upload(paperId, file, type);

      if (response.data && response.data.code === 200) {
        // 如果是当前选中的文献，更新其文件列表
        if (currentPaper()?.id === paperId) {
          const updatedPaper = await getPaperById(paperId);
          setCurrentPaper(updatedPaper);
        }

        return response.data.data;
      }
    } catch (err) {
      console.error('Failed to upload file:', err);
      setError(err.message);
      throw err;
    }
  };

  const deleteFile = async (paperId, filename) => {
    try {
      await filesAPI.delete(paperId, filename);

      // 如果是当前选中的文献，更新其文件列表
      if (currentPaper()?.id === paperId) {
        const updatedPaper = await getPaperById(paperId);
        setCurrentPaper(updatedPaper);
      }
    } catch (err) {
      console.error('Failed to delete file:', err);
      setError(err.message);
      throw err;
    }
  };

  const downloadFile = async (paperId, filename) => {
    try {
      const response = await filesAPI.download(paperId, filename);

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Failed to download file:', err);
      setError(err.message);
      throw err;
    }
  };

  // 组件挂载时加载初始数据
  onMount(() => {
    Promise.all([
      loadPapers(),
      loadFolderTree(),
      loadStats()
    ]);
  });

  // Context值
  const contextValue = {
    // 状态
    papers,
    loading,
    papersLoading,
    foldersLoading,
    error,
    pagination,
    searchQuery,
    filters,
    selectedPapers,
    selectMode,
    folders,
    folderTree,
    stats,
    currentPaper,

    // 操作方法
    loadPapers,
    setSearch,
    updateFilters,
    changePage,
    changePageSize,
    createPaper,
    updatePaper,
    deletePaper,
    getPaperById,
    togglePaperSelection,
    clearSelection,
    selectAllPapers,
    deleteSelectedPapers,
    loadFolderTree,
    createFolder,
    loadStats,
    reloadCache,

    // 文件管理方法
    uploadFile,
    deleteFile,
    downloadFile,

    // 状态设置方法
    setSearchQuery,
    setFilters,
    setSelectMode,
    setCurrentPaper,
    setError,
    setFolderTree
  };

  return (
    <PaperContext.Provider value={contextValue}>
      {props.children}
    </PaperContext.Provider>
  );
};

// Hook for using the context
export const usePaperContext = () => {
  const context = useContext(PaperContext);
  if (!context) {
    throw new Error('usePaperContext must be used within a PaperProvider');
  }
  return context;
};

export default PaperContext;
