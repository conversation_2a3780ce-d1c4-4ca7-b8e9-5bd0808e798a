# Lite Papers API 快速参考

## 基础信息
- **基础URL**: `http://127.0.0.1:3000`
- **数据格式**: JSON
- **认证**: 无需认证

## 核心端点

### 🏥 系统状态
```
GET  /health                           # 系统健康检查
```

### 📚 文献管理
```
GET    /api/papers                     # 获取文献列表（支持搜索和筛选）
POST   /api/papers                     # 创建新文献
GET    /api/papers/{id}                # 获取文献详情
PUT    /api/papers/{id}                # 更新文献
DELETE /api/papers/{id}                # 删除文献
```

**文献列表支持的参数:**
- `q` - 全文搜索（标题、摘要、关键词）
- `folder` - 文件夹筛选
- `year_start`, `year_end` - 年份范围筛选
- `sort_by` - 排序字段 (created_at|title|year|author)
- `sort_order` - 排序顺序 (asc|desc)
- `page`, `page_size` - 分页参数

### 📁 文件管理
```
GET    /api/papers/{id}/files          # 获取文件列表
POST   /api/papers/{id}/files          # 上传文件
GET    /api/papers/{id}/files/{name}   # 下载文件
GET    /api/papers/{id}/files/preview/{name}  # 预览文件
DELETE /api/papers/{id}/files/{name}   # 删除文件
```

### 📂 文件夹管理
```
GET    /api/folders                    # 获取文件夹列表
POST   /api/folders                    # 创建文件夹
DELETE /api/folders/{path}             # 删除文件夹
PUT    /api/folders/{path}             # 重命名文件夹
```

### 📊 统计分析
```
GET /api/papers/stats/detailed         # 详细统计信息
GET /api/papers/folders/tree           # 文件夹树结构
GET /api/papers/cache/stats            # 缓存统计
```

### ⚙️ 系统管理
```
POST /api/papers/cache/reload          # 重新加载缓存
POST /api/papers/migrate               # 数据迁移
```

## 快速示例

### 创建文献
```bash
curl -X POST http://127.0.0.1:3000/api/papers \
  -H "Content-Type: application/json" \
  -d '{
    "title": "论文标题",
    "authors": ["作者1", "作者2"],
    "year": 2023,
    "keywords": ["关键词1", "关键词2"],
    "folder_path": "AI Papers"
  }'
```

### 搜索和筛选文献
```bash
# 获取所有文献
curl "http://127.0.0.1:3000/api/papers"

# 全文搜索
curl "http://127.0.0.1:3000/api/papers?q=transformer"

# 按文件夹和年份筛选
curl "http://127.0.0.1:3000/api/papers?folder=AI Papers&year_start=2020"

# 组合搜索
curl "http://127.0.0.1:3000/api/papers?q=attention&year_start=2017&year_end=2020&sort_by=year"
```

### 上传文件
```bash
curl -X POST http://127.0.0.1:3000/api/papers/{id}/files?type=origin \
  -F "file=@paper.pdf"
```

### 创建文件夹
```bash
curl -X POST http://127.0.0.1:3000/api/folders \
  -H "Content-Type: application/json" \
  -d '{"name": "新文件夹", "parent_path": "父文件夹"}'
```

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": { ... }
}
```

### 分页响应
```json
{
  "papers": [...],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total": 100,
    "has_next": true,
    "has_prev": false
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述"
}
```

## 常用状态码
- `200` - 成功
- `201` - 创建成功
- `204` - 删除成功
- `400` - 请求错误
- `404` - 资源不存在
- `409` - 资源冲突
- `500` - 服务器错误

## 文件类型支持
- **图片**: jpg, jpeg, png, gif, bmp, webp, svg
- **文档**: pdf, txt, md, json
- **文件类型**: origin（原始论文）, note（笔记）, image（图片）

## 分页参数
- `page` - 页码（默认1）
- `page_size` - 每页数量（默认10，最大100）

## 排序选项
- **字段**: created_at, updated_at, title, year, author
- **顺序**: asc（升序）, desc（降序）

## 性能提示
- 所有响应包含 `X-Response-Time` 头信息
- 使用搜索过滤条件提高查询效率
- 合理设置分页大小
- 监控缓存状态以优化性能
