import { createSignal } from 'solid-js';
import {
  Button,
  Input,
  Card,
  Badge,
  Loading,
  Dropdown,
  DropdownItem,
  DropdownDivider,
  Modal,
  FileUpload,
  Table,
  Pagination,
  SearchBox,
  Checkbox,
  Tooltip,
  HighlightText,
  toast
} from '../ui';
import { Search, Settings, Download, Edit, Trash2, Eye } from 'lucide-solid';

const UIComponentsTest = () => {
  const [modalOpen, setModalOpen] = createSignal(false);
  const [inputValue, setInputValue] = createSignal('');
  const [selectedFiles, setSelectedFiles] = createSignal([]);
  const [searchValue, setSearchValue] = createSignal('');
  const [checkboxValue, setCheckboxValue] = createSignal(false);
  const [indeterminateValue, setIndeterminateValue] = createSignal(true);
  const [currentPage, setCurrentPage] = createSignal(1);
  const [pageSize, setPageSize] = createSignal(10);
  const [selectedRows, setSelectedRows] = createSignal([]);
  const [highlightQuery, setHighlightQuery] = createSignal('attention');

  // 表格数据
  const tableData = [
    { id: 1, name: '张三', age: 25, email: '<EMAIL>', status: '活跃' },
    { id: 2, name: '李四', age: 30, email: '<EMAIL>', status: '非活跃' },
    { id: 3, name: '王五', age: 28, email: '<EMAIL>', status: '活跃' },
    { id: 4, name: '赵六', age: 35, email: '<EMAIL>', status: '活跃' },
    { id: 5, name: '钱七', age: 22, email: '<EMAIL>', status: '非活跃' }
  ];

  // 表格列配置
  const tableColumns = [
    { key: 'name', title: '姓名', sortable: true },
    { key: 'age', title: '年龄', sortable: true },
    { key: 'email', title: '邮箱' },
    {
      key: 'status',
      title: '状态',
      render: (value) => (
        <Badge variant={value === '活跃' ? 'success' : 'secondary'}>
          {value}
        </Badge>
      )
    },
    {
      key: 'actions',
      title: '操作',
      render: (_, row) => (
        <div class="flex space-x-2">
          <Tooltip content="查看详情">
            <Button size="sm" variant="ghost">
              <Eye size={14} />
            </Button>
          </Tooltip>
          <Tooltip content="编辑">
            <Button size="sm" variant="ghost">
              <Edit size={14} />
            </Button>
          </Tooltip>
          <Tooltip content="删除">
            <Button size="sm" variant="ghost">
              <Trash2 size={14} />
            </Button>
          </Tooltip>
        </div>
      )
    }
  ];

  const handleFileSelect = (files) => {
    setSelectedFiles(files);
    console.log('Selected files:', files);
  };

  return (
    <div class="p-8 space-y-8 bg-gray-50 min-h-screen">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">UI 组件测试</h1>

        {/* 按钮组件 */}
        <Card header={<h2 class="text-xl font-semibold">按钮组件</h2>}>
          <div class="space-y-4">
            <div class="flex flex-wrap gap-4">
              <Button variant="primary">主要按钮</Button>
              <Button variant="secondary">次要按钮</Button>
              <Button variant="outline">边框按钮</Button>
              <Button variant="ghost">幽灵按钮</Button>
              <Button variant="danger">危险按钮</Button>
              <Button variant="success">成功按钮</Button>
            </div>
            
            <div class="flex flex-wrap gap-4">
              <Button size="sm">小按钮</Button>
              <Button size="md">中按钮</Button>
              <Button size="lg">大按钮</Button>
              <Button size="xl">超大按钮</Button>
            </div>
            
            <div class="flex flex-wrap gap-4">
              <Button loading>加载中</Button>
              <Button disabled>禁用按钮</Button>
            </div>
          </div>
        </Card>

        {/* 输入框组件 */}
        <Card header={<h2 class="text-xl font-semibold">输入框组件</h2>}>
          <div class="space-y-4">
            <Input
              label="基础输入框"
              placeholder="请输入内容"
              value={inputValue()}
              onInput={(e) => setInputValue(e.target.value)}
            />
            
            <Input
              label="带图标的输入框"
              placeholder="搜索..."
              leftIcon={<Search size={16} />}
            />
            
            <Input
              label="错误状态"
              placeholder="输入内容"
              error="这是一个错误信息"
            />
            
            <Input
              label="帮助文本"
              placeholder="输入内容"
              helperText="这是帮助文本"
            />
          </div>
        </Card>

        {/* 徽章组件 */}
        <Card header={<h2 class="text-xl font-semibold">徽章组件</h2>}>
          <div class="flex flex-wrap gap-4">
            <Badge>默认</Badge>
            <Badge variant="primary">主要</Badge>
            <Badge variant="success">成功</Badge>
            <Badge variant="warning">警告</Badge>
            <Badge variant="danger">危险</Badge>
            <Badge variant="info">信息</Badge>
            <Badge dot>带点</Badge>
          </div>
        </Card>

        {/* 加载组件 */}
        <Card header={<h2 class="text-xl font-semibold">加载组件</h2>}>
          <div class="space-y-4">
            <div class="flex items-center gap-8">
              <Loading size="sm" text="小尺寸" />
              <Loading size="md" text="中尺寸" />
              <Loading size="lg" text="大尺寸" />
              <Loading size="xl" text="超大尺寸" />
            </div>
            
            <div class="flex items-center gap-8">
              <Loading variant="spinner" text="旋转器" />
              <Loading variant="dots" text="点点" />
              <Loading variant="pulse" text="脉冲" />
            </div>
          </div>
        </Card>

        {/* 下拉菜单组件 */}
        <Card header={<h2 class="text-xl font-semibold">下拉菜单组件</h2>}>
          <div class="flex gap-4">
            <Dropdown
              trigger={
                <Button variant="outline">
                  <Settings size={16} class="mr-2" />
                  设置
                </Button>
              }
            >
              <DropdownItem onClick={() => console.log('个人资料')}>
                个人资料
              </DropdownItem>
              <DropdownItem onClick={() => console.log('账户设置')}>
                账户设置
              </DropdownItem>
              <DropdownDivider />
              <DropdownItem danger onClick={() => console.log('退出登录')}>
                退出登录
              </DropdownItem>
            </Dropdown>
          </div>
        </Card>

        {/* 模态框组件 */}
        <Card header={<h2 class="text-xl font-semibold">模态框组件</h2>}>
          <div class="space-y-4">
            <Button onClick={() => setModalOpen(true)}>
              打开模态框
            </Button>
            
            <Modal
              open={modalOpen()}
              onClose={() => setModalOpen(false)}
              title="测试模态框"
              size="md"
            >
              <div class="space-y-4">
                <p>这是一个测试模态框的内容。</p>
                <Input placeholder="在模态框中输入内容" />
                <div class="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setModalOpen(false)}>
                    取消
                  </Button>
                  <Button onClick={() => setModalOpen(false)}>
                    确定
                  </Button>
                </div>
              </div>
            </Modal>
          </div>
        </Card>

        {/* 文件上传组件 */}
        <Card header={<h2 class="text-xl font-semibold">文件上传组件</h2>}>
          <FileUpload
            multiple
            accept="image/*,.pdf,.txt"
            maxSize={5 * 1024 * 1024} // 5MB
            maxFiles={5}
            onFileSelect={handleFileSelect}
            showPreview
          />
          
          {selectedFiles().length > 0 && (
            <div class="mt-4">
              <p class="text-sm text-gray-600">
                已选择 {selectedFiles().length} 个文件
              </p>
            </div>
          )}
        </Card>

        {/* Toast 测试按钮 */}
        <Card header={<h2 class="text-xl font-semibold">Toast 通知</h2>}>
          <div class="flex gap-4">
            <Button onClick={() => toast.success('操作成功！')}>
              成功提示
            </Button>
            <Button onClick={() => toast.error('操作失败！')}>
              错误提示
            </Button>
            <Button onClick={() => toast.warning('警告信息！')}>
              警告提示
            </Button>
            <Button onClick={() => toast.info('信息提示！')}>
              信息提示
            </Button>
          </div>
        </Card>

        {/* 搜索框组件 */}
        <Card header={<h2 class="text-xl font-semibold">搜索框组件</h2>}>
          <div class="space-y-4">
            <SearchBox
              placeholder="搜索用户..."
              value={searchValue()}
              onChange={setSearchValue}
              onSearch={(value) => console.log('搜索:', value)}
              showHistory
              historyKey="user_search"
            />

            <SearchBox
              placeholder="带加载状态的搜索..."
              loading
              size="lg"
            />

            <SearchBox
              placeholder="禁用状态"
              disabled
              size="sm"
            />
          </div>
        </Card>

        {/* 复选框组件 */}
        <Card header={<h2 class="text-xl font-semibold">复选框组件</h2>}>
          <div class="space-y-4">
            <div class="space-y-2">
              <Checkbox
                checked={checkboxValue()}
                onChange={setCheckboxValue}
                label="基础复选框"
                description="这是一个基础的复选框示例"
              />

              <Checkbox
                checked={indeterminateValue()}
                indeterminate
                label="不确定状态"
                description="这是一个不确定状态的复选框"
              />

              <Checkbox
                disabled
                label="禁用状态"
                description="这是一个禁用的复选框"
              />
            </div>

            <div class="flex gap-4">
              <Checkbox variant="success" checked label="成功" />
              <Checkbox variant="warning" checked label="警告" />
              <Checkbox variant="danger" checked label="危险" />
            </div>

            <div class="flex gap-4">
              <Checkbox size="sm" checked label="小尺寸" />
              <Checkbox size="md" checked label="中尺寸" />
              <Checkbox size="lg" checked label="大尺寸" />
            </div>
          </div>
        </Card>

        {/* Tooltip 组件 */}
        <Card header={<h2 class="text-xl font-semibold">Tooltip 组件</h2>}>
          <div class="space-y-4">
            <div class="flex gap-4">
              <Tooltip content="这是一个顶部提示" placement="top">
                <Button>顶部提示</Button>
              </Tooltip>

              <Tooltip content="这是一个底部提示" placement="bottom">
                <Button>底部提示</Button>
              </Tooltip>

              <Tooltip content="这是一个左侧提示" placement="left">
                <Button>左侧提示</Button>
              </Tooltip>

              <Tooltip content="这是一个右侧提示" placement="right">
                <Button>右侧提示</Button>
              </Tooltip>
            </div>

            <div class="flex gap-4">
              <Tooltip content="点击触发" trigger="click">
                <Button variant="outline">点击触发</Button>
              </Tooltip>

              <Tooltip content="焦点触发" trigger="focus">
                <Input placeholder="焦点触发提示" />
              </Tooltip>
            </div>
          </div>
        </Card>

        {/* 表格组件 */}
        <Card header={<h2 class="text-xl font-semibold">表格组件</h2>}>
          <div class="space-y-4">
            <Table
              columns={tableColumns}
              data={tableData}
              sortable
              selectable
              selectedRows={selectedRows()}
              onSelect={setSelectedRows}
              onSort={(key, direction) => console.log('排序:', key, direction)}
            />

            {selectedRows().length > 0 && (
              <div class="text-sm text-gray-600">
                已选择 {selectedRows().length} 行
              </div>
            )}
          </div>
        </Card>

        {/* 分页组件 */}
        <Card header={<h2 class="text-xl font-semibold">分页组件</h2>}>
          <div class="space-y-4">
            <Pagination
              current={currentPage()}
              total={250}
              pageSize={pageSize()}
              showSizeChanger
              showQuickJumper
              showTotal
              onChange={(page, size) => {
                setCurrentPage(page);
                console.log('页码变化:', page, size);
              }}
              onShowSizeChange={(page, size) => {
                setPageSize(size);
                setCurrentPage(1);
                console.log('页面大小变化:', page, size);
              }}
            />

            <Pagination
              current={1}
              total={50}
              pageSize={10}
              size="sm"
            />

            <Pagination
              current={1}
              total={500}
              pageSize={20}
              size="lg"
              showTotal
            />
          </div>
        </Card>
      </div>
    </div>
  );
};

export default UIComponentsTest;
