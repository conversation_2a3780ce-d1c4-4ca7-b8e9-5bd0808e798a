import { createSignal, For, createMemo, Show } from 'solid-js';
import { Folder, FolderOpen, ChevronRight, ChevronDown, FileText } from 'lucide-solid';
import { formatFolderPath } from '../../utils/folderUtils';

/**
 * 通用文件夹树组件
 * @param {Object} props - 组件属性
 * @param {Array} props.folderTree - 文件夹树数据
 * @param {string|null} props.selectedFolder - 当前选中的文件夹路径
 * @param {Function} props.onSelect - 选择文件夹的回调函数
 * @param {boolean} props.showAllPapersOption - 是否显示全部文献选项，默认 false
 * @param {boolean} props.showPaperCount - 是否显示文献数量，默认 true
 * @param {boolean} props.expandable - 是否支持展开/收起，默认 true
 * @param {number} props.maxDepth - 最大展开深度，默认 5
 * @param {string} props.variant - 样式变体：'default' | 'sidebar' | 'selector'
 * @param {Object} props.customStyles - 自定义样式类
 */
const FolderTree = (props) => {
  const [expandedFolders, setExpandedFolders] = createSignal(new Set());
  
  // 默认配置
  const config = {
    showAllPapersOption: props.showAllPapersOption ?? false,
    showPaperCount: props.showPaperCount ?? true,
    expandable: props.expandable ?? true,
    maxDepth: props.maxDepth ?? 5,
    variant: props.variant ?? 'default'
  };

  // 样式变体配置
  const getVariantStyles = () => {
    const baseStyles = {
      container: 'space-y-1',
      item: 'flex items-center px-3 py-2 rounded-lg cursor-pointer transition-colors group relative',
      itemSelected: 'bg-theme-accent text-theme-accent border border-theme-accent',
      itemHover: 'hover:bg-theme-muted text-theme-secondary hover:text-theme-primary',
      icon: 'mr-2',
      iconSelected: 'text-theme-accent',
      iconDefault: 'text-theme-muted',
      text: 'flex-1 text-sm truncate font-medium',
      count: 'inline-flex items-center justify-center min-w-[18px] h-[18px] px-1 bg-theme-muted text-theme-primary text-xs font-medium rounded-full',
      expandButton: 'p-1 rounded hover:bg-theme-muted transition-colors'
    };

    switch (config.variant) {
      case 'sidebar':
        return {
          ...baseStyles,
          itemSelected: 'sidebar-item-active-theme',
          itemHover: 'sidebar-item-theme hover:bg-theme-muted',
          iconSelected: 'text-orange-500'
        };
      case 'selector':
        return baseStyles;
      default:
        return baseStyles;
    }
  };

  const styles = getVariantStyles();

  // 切换文件夹展开状态
  const toggleFolder = (folderPath) => {
    if (!config.expandable) return;
    
    const expanded = new Set(expandedFolders());
    if (expanded.has(folderPath)) {
      expanded.delete(folderPath);
    } else {
      expanded.add(folderPath);
    }
    setExpandedFolders(expanded);
  };

  // 选择文件夹
  const selectFolder = (folderPath) => {
    if (props.onSelect) {
      props.onSelect(folderPath);
    }
  };

  // 递归文件夹项组件
  const FolderItem = (itemProps) => {
    const depth = itemProps.depth || 0;
    const folder = itemProps.folder;

    const isExpanded = createMemo(() => expandedFolders().has(folder.path));
    const isSelected = createMemo(() => props.selectedFolder === folder.path);
    const hasChildren = createMemo(() => folder.subfolders && folder.subfolders.length > 0);

    return (
      <div class="mb-1">
        <div
          class={`${styles.item} ${
            isSelected() ? styles.itemSelected : styles.itemHover
          }`}
          onClick={() => selectFolder(folder.path)}
          style={{ 'margin-left': `${depth * 16}px` }}
        >
          {/* 文件夹图标 */}
          {isExpanded() && hasChildren() ? (
            <FolderOpen 
              size={16} 
              class={`${styles.icon} ${isSelected() ? styles.iconSelected : styles.iconDefault}`} 
            />
          ) : (
            <Folder 
              size={16} 
              class={`${styles.icon} ${isSelected() ? styles.iconSelected : styles.iconDefault}`} 
            />
          )}

          {/* 文件夹名称 */}
          <span class={styles.text}>
            {folder.name}
          </span>

          {/* 右侧内容区域 */}
          <div class="flex items-center space-x-2">
            {/* 文献数量 */}
            <Show when={config.showPaperCount}>
              {folder.papers_count > 0 ? (
                <span class={styles.count}>
                  {folder.papers_count}
                </span>
              ) : (
                config.variant === 'sidebar' && (
                  <div class="w-2 h-2 bg-theme-muted rounded-full opacity-50" title="无文献" />
                )
              )}
            </Show>

            {/* 展开/收起按钮 */}
            <Show when={config.expandable && hasChildren()}>
              <button
                class={styles.expandButton}
                onClick={(e) => {
                  e.stopPropagation();
                  toggleFolder(folder.path);
                }}
                title={isExpanded() ? '收起文件夹' : '展开文件夹'}
              >
                {isExpanded() ? (
                  <ChevronDown size={14} class="text-theme-muted" />
                ) : (
                  <ChevronRight size={14} class="text-theme-muted" />
                )}
              </button>
            </Show>
          </div>
        </div>

        {/* 子文件夹 */}
        <Show when={config.expandable && hasChildren() && isExpanded() && depth < config.maxDepth}>
          <div class="mt-1 space-y-1">
            <For each={folder.subfolders}>
              {(child) => (
                <FolderItem
                  folder={child}
                  depth={depth + 1}
                />
              )}
            </For>
          </div>
        </Show>
      </div>
    );
  };

  return (
    <div class={styles.container}>
      {/* 全部文献选项 */}
      <Show when={config.showAllPapersOption}>
        <div
          class={`${styles.item} ${
            props.selectedFolder === null ? styles.itemSelected : styles.itemHover
          }`}
          onClick={() => selectFolder(null)}
        >
          <FileText 
            size={16} 
            class={`${styles.icon} ${props.selectedFolder === null ? styles.iconSelected : styles.iconDefault}`} 
          />
          <span class={styles.text}>
            全部文献
          </span>
        </div>
      </Show>

      {/* 文件夹树 */}
      <For each={props.folderTree || []}>
        {(folder) => <FolderItem folder={folder} depth={0} />}
      </For>
    </div>
  );
};

export default FolderTree;
