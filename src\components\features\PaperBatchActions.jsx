import { FolderOpen, Trash2 } from 'lucide-solid';
import { Button } from '../ui';
import { Show } from 'solid-js';

const PaperBatchActions = (props) => {

  return (
    <Show when={props.action}>
    <div class="bg-theme-primary border-b border-theme-primary px-6 py-3">
      <div class="flex items-center justify-between">
        <span class="text-sm text-theme-secondary">
          已选择 {props.selectedCount} 个文献
        </span>

        <div class="flex items-center space-x-3">
          {/* 移动按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={props.onMove}
            disabled={props.selectedCount === 0}
          >
            <FolderOpen size={14} class="mr-1" />
            移动到文件夹
          </Button>

          {/* 删除按钮 */}
          <Button
            variant="danger"
            size="sm"
            onClick={props.onDelete}
            disabled={props.selectedCount === 0}
          >
            <Trash2 size={14} class="mr-1" />
            删除
          </Button>

          {/* 取消选择 */}
          <button
            onClick={props.onClearSelection}
            class="text-sm text-theme-muted hover:text-theme-primary"
          >
            取消选择
          </button>
        </div>
      </div>
    </div>
    </Show>
  );
};

export default PaperBatchActions;
