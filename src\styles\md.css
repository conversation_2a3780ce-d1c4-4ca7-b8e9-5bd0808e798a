/* Markdown 预览样式 - 简洁版 */

/* 基础容器样式 */
.markdown-preview {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: #374151;
  font-size: 14px;
  word-wrap: break-word;
  max-width: 100%;
}

.dark .markdown-preview {
  color: #e5e7eb;
}

/* 表格样式 */
.markdown-preview table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
  border: 1px solid #e5e7eb;
}

.markdown-preview th,
.markdown-preview td {
  border: 1px solid #e5e7eb;
  padding: 0.5rem 0.75rem;
  text-align: left;
}

.markdown-preview th {
  background-color: #f9fafb;
  font-weight: 600;
}

/* 深色主题表格样式 */
.dark .markdown-preview table {
  border-color: #4b5563;
}

.dark .markdown-preview th {
  background-color: #374151;
  border-color: #4b5563;
}

.dark .markdown-preview th,
.dark .markdown-preview td {
  border-color: #4b5563;
}

/* 引用块样式 */
.markdown-preview blockquote {
  border-left: 4px solid #d1d5db;
  margin: 1rem 0;
  padding: 0.75rem 1rem;
  background-color: #f9fafb;
  color: #6b7280;
}

.markdown-preview blockquote p {
  margin: 0;
}

.dark .markdown-preview blockquote {
  border-left-color: #4b5563;
  background-color: #374151;
  color: #9ca3af;
}

/* 行内代码样式 */
.markdown-preview code {
  background-color: #f3f4f6;
  color: #374151;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  font-size: 0.875em;
  font-family: 'Courier New', Courier, monospace;
}

.dark .markdown-preview code {
  background-color: #4b5563;
  color: #e5e7eb;
}

/* 代码块样式 */
.markdown-preview pre {
  background-color: #f3f4f6;
  color: #374151;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-preview pre code {
  background: transparent;
  color: inherit;
  padding: 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

.dark .markdown-preview pre {
  background-color: #374151;
  color: #e5e7eb;
}

/* 标题样式 */
.markdown-preview h1,
.markdown-preview h2,
.markdown-preview h3,
.markdown-preview h4,
.markdown-preview h5,
.markdown-preview h6 {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  line-height: 1.25;
  color: #1f2937;
}

.dark .markdown-preview h1,
.dark .markdown-preview h2,
.dark .markdown-preview h3,
.dark .markdown-preview h4,
.dark .markdown-preview h5,
.dark .markdown-preview h6 {
  color: #f9fafb;
}

.markdown-preview h1 {
  font-size: 1.875rem;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.markdown-preview h2 {
  font-size: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.25rem;
}

.markdown-preview h3 {
  font-size: 1.25rem;
}

.markdown-preview h4 {
  font-size: 1.125rem;
}

.markdown-preview h5 {
  font-size: 1rem;
}

.markdown-preview h6 {
  font-size: 0.875rem;
  color: #6b7280;
}

.dark .markdown-preview h1,
.dark .markdown-preview h2 {
  border-bottom-color: #4b5563;
}

.dark .markdown-preview h6 {
  color: #9ca3af;
}

/* 列表样式 */
.markdown-preview ul,
.markdown-preview ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.markdown-preview li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

.markdown-preview ul ul,
.markdown-preview ol ol,
.markdown-preview ul ol,
.markdown-preview ol ul {
  margin: 0.5rem 0;
}

/* 任务列表样式 */
.markdown-preview input[type="checkbox"] {
  margin-right: 0.5rem;
}

/* 链接样式 */
.markdown-preview a {
  color: #3b82f6;
  text-decoration: underline;
}

.markdown-preview a:hover {
  color: #1d4ed8;
}

.dark .markdown-preview a {
  color: #60a5fa;
}

.dark .markdown-preview a:hover {
  color: #93c5fd;
}

/* 分隔线样式 */
.markdown-preview hr {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 2rem 0;
}

.dark .markdown-preview hr {
  border-top-color: #4b5563;
}

/* 图片样式 */
.markdown-preview img {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
  display: block;
}

/* 段落样式 */
.markdown-preview p {
  margin: 1rem 0;
  line-height: 1.6;
}

/* 强调样式 */
.markdown-preview strong {
  font-weight: 700;
}

.markdown-preview em {
  font-style: italic;
}

/* 删除线样式 */
.markdown-preview del {
  text-decoration: line-through;
  opacity: 0.7;
}
