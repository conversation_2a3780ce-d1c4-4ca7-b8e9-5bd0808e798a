import { splitProps } from 'solid-js';

const Loading = (props) => {
  const [local, others] = splitProps(props, [
    'size',
    'variant',
    'text',
    'overlay',
    'class'
  ]);

  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  const variants = {
    spinner: 'border-2 border-current border-t-transparent rounded-full animate-spin',
    dots: 'flex space-x-1',
    pulse: 'bg-current rounded-full animate-pulse'
  };

  const size = local.size || 'md';
  const variant = local.variant || 'spinner';

  const SpinnerComponent = () => (
    <div class={`${variants.spinner} ${sizes[size]} ${local.class || ''}`} {...others} />
  );

  const DotsComponent = () => (
    <div class={`${variants.dots} ${local.class || ''}`} {...others}>
      <div class="w-2 h-2 bg-current rounded-full animate-bounce" style="animation-delay: 0ms" />
      <div class="w-2 h-2 bg-current rounded-full animate-bounce" style="animation-delay: 150ms" />
      <div class="w-2 h-2 bg-current rounded-full animate-bounce" style="animation-delay: 300ms" />
    </div>
  );

  const PulseComponent = () => (
    <div class={`${variants.pulse} ${sizes[size]} ${local.class || ''}`} {...others} />
  );

  const LoadingIcon = () => {
    switch (variant) {
      case 'dots':
        return <DotsComponent />;
      case 'pulse':
        return <PulseComponent />;
      default:
        return <SpinnerComponent />;
    }
  };

  if (local.overlay) {
    return (
      <div class="fixed inset-0 modal-overlay-theme flex items-center justify-center z-50">
        <div class="modal-theme rounded-lg p-6 flex flex-col items-center space-y-4">
          <LoadingIcon />
          {local.text && (
            <p class="text-theme-secondary text-sm">{local.text}</p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div class="flex items-center space-x-2">
      <LoadingIcon />
      {local.text && (
        <span class="text-theme-secondary text-sm">{local.text}</span>
      )}
    </div>
  );
};

export default Loading;
