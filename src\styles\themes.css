/* 主题色彩系统 - 基于 CSS 变量 */

/* 浅色主题 (默认) */
:root {
  /* 背景色 */
  --bg-primary: 255 255 255;           /* 主背景 - 白色 */
  --bg-secondary: 249 250 251;         /* 次要背景 - 极浅灰 */
  --bg-elevated: 255 255 255;          /* 悬浮背景 - 白色 */
  --bg-muted: 243 244 246;             /* 静音背景 - 浅灰 */
  --bg-accent: 239 246 255;            /* 强调背景 - 浅蓝 */
  
  /* 文字色 */
  --text-primary: 17 24 39;            /* 主文字 - 深灰 */
  --text-secondary: 75 85 99;          /* 次要文字 - 中灰 */
  --text-muted: 107 114 128;           /* 静音文字 - 浅灰 */
  --text-inverse: 255 255 255;         /* 反色文字 - 白色 */
  --text-accent: 59 130 246;           /* 强调文字 - 蓝色 */
  
  /* 边框色 */
  --border-primary: 229 231 235;       /* 主边框 - 浅灰 */
  --border-secondary: 209 213 219;     /* 次要边框 - 中灰 */
  --border-muted: 243 244 246;         /* 静音边框 - 极浅灰 */
  --border-accent: 147 197 253;        /* 强调边框 - 浅蓝 */
  
  /* 状态色 */
  --success: 34 197 94;                /* 成功 - 绿色 */
  --success-bg: 240 253 244;           /* 成功背景 */
  --success-border: 187 247 208;       /* 成功边框 */
  
  --error: 239 68 68;                  /* 错误 - 红色 */
  --error-bg: 254 242 242;             /* 错误背景 */
  --error-border: 252 165 165;         /* 错误边框 */
  
  --warning: 245 158 11;               /* 警告 - 橙色 */
  --warning-bg: 255 251 235;           /* 警告背景 */
  --warning-border: 253 230 138;       /* 警告边框 */
  
  --info: 59 130 246;                  /* 信息 - 蓝色 */
  --info-bg: 239 246 255;              /* 信息背景 */
  --info-border: 147 197 253;          /* 信息边框 */
  
  /* 主题色 */
  --primary: 59 130 246;               /* 主色 - 蓝色 */
  --primary-hover: 37 99 235;          /* 主色悬浮 */
  --primary-bg: 239 246 255;           /* 主色背景 */
  --primary-border: 147 197 253;       /* 主色边框 */
  
  --secondary: 107 114 128;            /* 次要色 - 灰色 */
  --secondary-hover: 75 85 99;         /* 次要色悬浮 */
  --secondary-bg: 249 250 251;         /* 次要色背景 */
  --secondary-border: 209 213 219;     /* 次要色边框 */
  
  /* 特殊功能色 */
  --shadow: 0 0 0;                     /* 阴影色 - 黑色 */
  --overlay: 0 0 0;                    /* 遮罩色 - 黑色 */
  --highlight: 254 240 138;            /* 高亮色 - 黄色 */
  
  /* 文献卡片专用色 */
  --paper-bg: 255 255 255;             /* 文献卡片背景 */
  --paper-border: 229 231 235;         /* 文献卡片边框 */
  --paper-hover-bg: 249 250 251;       /* 文献卡片悬浮背景 */
  --paper-hover-border: 209 213 219;   /* 文献卡片悬浮边框 */
  
  /* 侧边栏色 */
  --sidebar-bg: 249 250 251;           /* 侧边栏背景 */
  --sidebar-border: 229 231 235;       /* 侧边栏边框 */
  --sidebar-item-hover: 243 244 246;   /* 侧边栏项目悬浮 */
  --sidebar-item-active: 239 246 255;  /* 侧边栏项目激活 */
  
  /* 头部色 */
  --header-bg: 255 255 255;            /* 头部背景 */
  --header-border: 229 231 235;        /* 头部边框 */
  
  /* 输入框色 */
  --input-bg: 255 255 255;             /* 输入框背景 */
  --input-border: 209 213 219;         /* 输入框边框 */
  --input-focus-border: 59 130 246;    /* 输入框焦点边框 */
  --input-focus-ring: 147 197 253;     /* 输入框焦点环 */
  
  /* 按钮色 */
  --btn-primary-bg: 59 130 246;        /* 主按钮背景 */
  --btn-primary-hover: 37 99 235;      /* 主按钮悬浮 */
  --btn-primary-text: 255 255 255;     /* 主按钮文字 */
  
  --btn-secondary-bg: 243 244 246;     /* 次要按钮背景 */
  --btn-secondary-hover: 229 231 235;  /* 次要按钮悬浮 */
  --btn-secondary-text: 75 85 99;      /* 次要按钮文字 */
  
  --btn-outline-border: 209 213 219;   /* 轮廓按钮边框 */
  --btn-outline-hover: 249 250 251;    /* 轮廓按钮悬浮 */
  
  /* 模态框色 */
  --modal-bg: 255 255 255;             /* 模态框背景 */
  --modal-overlay: 0 0 0;              /* 模态框遮罩 */
  --modal-border: 229 231 235;         /* 模态框边框 */
}

/* 深色主题 */
[data-theme="dark"] {
  /* 背景色 */
  --bg-primary: 0 0 0;                 /* 主背景 - 纯黑 */
  --bg-secondary: 15 15 15;            /* 次要背景 - 深黑 */
  --bg-elevated: 25 25 25;             /* 悬浮背景 - 深灰 */
  --bg-muted: 35 35 35;                /* 静音背景 - 中深灰 */
  --bg-accent: 20 40 80;               /* 强调背景 - 深蓝 */
  
  /* 文字色 */
  --text-primary: 249 250 251;         /* 主文字 - 浅灰 */
  --text-secondary: 209 213 219;       /* 次要文字 - 中浅灰 */
  --text-muted: 156 163 175;           /* 静音文字 - 中灰 */
  --text-inverse: 17 24 39;            /* 反色文字 - 深灰 */
  --text-accent: 96 165 250;           /* 强调文字 - 浅蓝 */
  
  /* 边框色 */
  --border-primary: 45 45 45;          /* 主边框 - 深灰 */
  --border-secondary: 60 60 60;        /* 次要边框 - 中深灰 */
  --border-muted: 25 25 25;            /* 静音边框 - 深黑 */
  --border-accent: 59 130 246;         /* 强调边框 - 蓝色 */
  
  /* 状态色 */
  --success: 34 197 94;                /* 成功 - 绿色 */
  --success-bg: 15 40 25;              /* 成功背景 */
  --success-border: 34 197 94;         /* 成功边框 */

  --error: 248 113 113;                /* 错误 - 浅红色 */
  --error-bg: 40 15 15;                /* 错误背景 */
  --error-border: 239 68 68;           /* 错误边框 */

  --warning: 251 191 36;               /* 警告 - 浅橙色 */
  --warning-bg: 40 30 10;              /* 警告背景 */
  --warning-border: 245 158 11;        /* 警告边框 */

  --info: 96 165 250;                  /* 信息 - 浅蓝色 */
  --info-bg: 20 40 80;                 /* 信息背景 */
  --info-border: 59 130 246;           /* 信息边框 */
  
  /* 主题色 */
  --primary: 96 165 250;               /* 主色 - 浅蓝色 */
  --primary-hover: 59 130 246;         /* 主色悬浮 */
  --primary-bg: 20 40 80;              /* 主色背景 */
  --primary-border: 59 130 246;        /* 主色边框 */

  --secondary: 156 163 175;            /* 次要色 - 中灰 */
  --secondary-hover: 209 213 219;      /* 次要色悬浮 */
  --secondary-bg: 35 35 35;            /* 次要色背景 */
  --secondary-border: 60 60 60;        /* 次要色边框 */
  
  /* 特殊功能色 */
  --shadow: 0 0 0;                     /* 阴影色 - 黑色 */
  --overlay: 0 0 0;                    /* 遮罩色 - 黑色 */
  --highlight: 202 138 4;              /* 高亮色 - 深黄色 */
  
  /* 文献卡片专用色 */
  --paper-bg: 25 25 25;                /* 文献卡片背景 */
  --paper-border: 45 45 45;            /* 文献卡片边框 */
  --paper-hover-bg: 35 35 35;          /* 文献卡片悬浮背景 */
  --paper-hover-border: 60 60 60;      /* 文献卡片悬浮边框 */

  /* 侧边栏色 */
  --sidebar-bg: 15 15 15;              /* 侧边栏背景 */
  --sidebar-border: 45 45 45;          /* 侧边栏边框 */
  --sidebar-item-hover: 35 35 35;      /* 侧边栏项目悬浮 */
  --sidebar-item-active: 20 40 80;     /* 侧边栏项目激活 */

  /* 头部色 */
  --header-bg: 15 15 15;               /* 头部背景 */
  --header-border: 45 45 45;           /* 头部边框 */
  
  /* 输入框色 */
  --input-bg: 35 35 35;                /* 输入框背景 */
  --input-border: 60 60 60;            /* 输入框边框 */
  --input-focus-border: 96 165 250;    /* 输入框焦点边框 */
  --input-focus-ring: 59 130 246;      /* 输入框焦点环 */

  /* 按钮色 */
  --btn-primary-bg: 59 130 246;        /* 主按钮背景 */
  --btn-primary-hover: 37 99 235;      /* 主按钮悬浮 */
  --btn-primary-text: 255 255 255;     /* 主按钮文字 */

  --btn-secondary-bg: 35 35 35;        /* 次要按钮背景 */
  --btn-secondary-hover: 60 60 60;     /* 次要按钮悬浮 */
  --btn-secondary-text: 209 213 219;   /* 次要按钮文字 */

  --btn-outline-border: 60 60 60;      /* 轮廓按钮边框 */
  --btn-outline-hover: 35 35 35;       /* 轮廓按钮悬浮 */

  /* 模态框色 */
  --modal-bg: 25 25 25;                /* 模态框背景 */
  --modal-overlay: 0 0 0;              /* 模态框遮罩 */
  --modal-border: 45 45 45;            /* 模态框边框 */
}

/* 主题切换动画 */
* {
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* 禁用某些元素的过渡动画以提高性能 */
.no-transition,
.no-transition * {
  transition: none !important;
}
