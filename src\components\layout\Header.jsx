import { createSignal } from 'solid-js';
import { Menu, Plus, Settings, Wifi, WifiOff } from 'lucide-solid';
import { Button, SearchBox, ThemeToggle } from '../ui';

const Header = (props) => {
  const [searchQuery, setSearchQuery] = createSignal('');
  let searchTimeout;



  // 处理搜索输入变化
  const handleSearchInput = (value) => {
    setSearchQuery(value);

    // 实时搜索（防抖）
    if (props.onGlobalSearch) {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        // 无论是否有内容都调用搜索，清空时传递空字符串
        props.onGlobalSearch(value.trim());
      }, 300);
    }
  };

  // 快捷键支持
  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      setSearchQuery('');
      // 清空搜索时也要通知父组件
      if (props.onGlobalSearch) {
        props.onGlobalSearch('');
      }
      e.target.blur();
    }
  };



  return (
    <header class="header-theme border-b px-4 py-3 flex items-center justify-between shadow-theme-sm">
      {/* Left Section */}
      <div class="flex items-center space-x-4">
        {/* Menu Button */}
        <Button
          onClick={props.onToggleSidebar}
          variant="ghost"
          size="sm"
          title={props.sidebarCollapsed ? '展开侧边栏' : '收起侧边栏'}
        >
          <Menu size={20} />
        </Button>
        
        {/* Logo & Title */}
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-neutral-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-sm">LP</span>
          </div>
          <h1 class="text-xl font-semibold text-theme-primary">Lite Papers</h1>
        </div>
      </div>

      {/* Center Section - Global Search */}
      <div class="flex-1 max-w-lg mx-8">
        <SearchBox
          value={searchQuery()}
          onChange={handleSearchInput}
          onKeyDown={handleKeyDown}
          placeholder="搜索文献、作者、关键词..."
          showShortcut={true}
          shortcutText="Ctrl+K"
        />
      </div>

      {/* Right Section */}
      <div class="flex items-center space-x-3">
        {/* API Status */}
        <div class="flex items-center space-x-2">
          {props.apiConnected ? (
            <>
              <Wifi size={16} class="text-green-500" />
              <span class="text-sm text-green-600 hidden sm:inline">已连接</span>
            </>
          ) : (
            <>
              <WifiOff size={16} class="text-red-500" />
              <span class="text-sm text-red-600 hidden sm:inline">未连接</span>
            </>
          )}
        </div>

        {/* Add Paper Button */}
        <Button
          onClick={props.onAddPaper}
          variant="primary"
          size="sm"
        >
          <Plus size={16} />
          <span class="hidden sm:inline">添加文献</span>
        </Button>

        {/* Theme Toggle */}
        <ThemeToggle simple={true} />

        {/* Settings Button */}
        <Button variant="ghost" size="sm">
          <Settings size={20} />
        </Button>
      </div>


    </header>
  );
};

export default Header;
