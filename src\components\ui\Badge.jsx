import { splitProps } from 'solid-js';

const Badge = (props) => {
  const [local, others] = splitProps(props, [
    'children',
    'variant',
    'size',
    'dot',
    'class'
  ]);

  const baseClasses = 'inline-flex items-center font-medium rounded-full transition-colors duration-200';

  const variants = {
    default: 'bg-theme-muted text-theme-primary border border-theme-secondary',
    primary: 'bg-theme-accent text-white border border-theme-accent',
    secondary: 'bg-theme-secondary text-theme-primary border border-theme-primary',
    outline: 'border border-theme-secondary bg-transparent text-theme-secondary hover:bg-theme-muted',
    success: 'bg-green-100 text-green-800 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800',
    warning: 'bg-yellow-100 text-yellow-800 border border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800',
    danger: 'bg-red-100 text-red-800 border border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',
    info: 'bg-blue-100 text-blue-800 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800'
  };

  const sizes = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-sm'
  };

  const variant = local.variant || 'default';
  const size = local.size || 'md';

  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${local.class || ''}`;

  return (
    <span class={classes} {...others}>
      {local.dot && (
        <div class="w-1.5 h-1.5 bg-current rounded-full mr-1.5" />
      )}
      {local.children}
    </span>
  );
};

export default Badge;
