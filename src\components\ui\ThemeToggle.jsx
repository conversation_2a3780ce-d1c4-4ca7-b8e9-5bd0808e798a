import { <PERSON>, <PERSON>, Monitor } from 'lucide-solid';
import { useTheme } from '../../stores/ThemeContext';
import { Button } from './index';

const ThemeToggle = (props) => {
  const { theme, setTheme, isDark, isLight, isAuto, THEMES } = useTheme();

  // 主题选项配置
  const themeOptions = [
    {
      value: THEMES.LIGHT,
      label: '浅色',
      icon: Sun,
      description: '始终使用浅色主题'
    },
    {
      value: THEMES.DARK,
      label: '深色',
      icon: Moon,
      description: '始终使用深色主题'
    },
    {
      value: THEMES.AUTO,
      label: '跟随系统',
      icon: Monitor,
      description: '跟随系统主题设置'
    }
  ];

  // 获取当前主题的图标
  const getCurrentIcon = () => {
    if (isAuto()) return Monitor;
    if (isDark()) return Moon;
    return Sun;
  };

  // 简单切换模式（只在 light/dark 之间切换）
  if (props.simple) {
    return (
      <Button
        variant="ghost"
        size={props.size || "sm"}
        onClick={() => setTheme(isDark() ? THEMES.LIGHT : THEMES.DARK)}
        title={`切换到${isDark() ? '浅色' : '深色'}主题`}
        class={props.class}
      >
        {(() => {
          const CurrentIcon = getCurrentIcon();
          return <CurrentIcon size={props.iconSize || 20} />;
        })()}
        {props.showLabel && (
          <span class="ml-2">
            {isDark() ? '深色' : '浅色'}
          </span>
        )}
      </Button>
    );
  }

  // 下拉菜单模式
  return (
    <div class={`relative ${props.class || ''}`}>
      <Button
        variant="ghost"
        size={props.size || "sm"}
        onClick={() => {
          // 简单的三态切换
          const currentTheme = theme();
          if (currentTheme === THEMES.LIGHT) {
            setTheme(THEMES.DARK);
          } else if (currentTheme === THEMES.DARK) {
            setTheme(THEMES.AUTO);
          } else {
            setTheme(THEMES.LIGHT);
          }
        }}
        title="切换主题"
      >
        {(() => {
          const CurrentIcon = getCurrentIcon();
          return <CurrentIcon size={props.iconSize || 20} />;
        })()}
        {props.showLabel && (
          <span class="ml-2">
            {isAuto() ? '跟随系统' : isDark() ? '深色' : '浅色'}
          </span>
        )}
      </Button>
    </div>
  );
};

// 主题选择器组件（完整版本）
export const ThemeSelector = (props) => {
  const { theme, setTheme, THEMES } = useTheme();

  const themeOptions = [
    {
      value: THEMES.LIGHT,
      label: '浅色主题',
      icon: Sun,
      description: '始终使用浅色主题'
    },
    {
      value: THEMES.DARK,
      label: '深色主题',
      icon: Moon,
      description: '始终使用深色主题'
    },
    {
      value: THEMES.AUTO,
      label: '跟随系统',
      icon: Monitor,
      description: '跟随系统主题设置'
    }
  ];

  return (
    <div class={`space-y-2 ${props.class || ''}`}>
      {props.title && (
        <h3 class="text-theme-primary font-medium text-sm">{props.title}</h3>
      )}
      
      <div class="space-y-1">
        {themeOptions.map((option) => {
          const Icon = option.icon;
          const isSelected = theme() === option.value;
          
          return (
            <button
              key={option.value}
              onClick={() => setTheme(option.value)}
              class={`
                w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors
                ${isSelected 
                  ? 'bg-theme-accent text-theme-accent border border-theme-accent' 
                  : 'hover:bg-theme-muted text-theme-secondary hover:text-theme-primary'
                }
              `}
            >
              <Icon size={16} />
              <div class="flex-1">
                <div class="font-medium text-sm">{option.label}</div>
                {props.showDescription && (
                  <div class="text-xs text-theme-muted">{option.description}</div>
                )}
              </div>
              {isSelected && (
                <div class="w-2 h-2 bg-theme-accent rounded-full"></div>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};

// 主题状态指示器
export const ThemeIndicator = (props) => {
  const { isDark, isLight, isAuto } = useTheme();

  return (
    <div class={`flex items-center space-x-1 ${props.class || ''}`}>
      <div class={`w-2 h-2 rounded-full ${
        isDark() ? 'bg-gray-800' : 'bg-yellow-400'
      }`}></div>
      <span class="text-xs text-theme-muted">
        {isAuto() ? '自动' : isDark() ? '深色' : '浅色'}
      </span>
    </div>
  );
};

export default ThemeToggle;
