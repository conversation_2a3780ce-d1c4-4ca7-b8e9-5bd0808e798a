import { For, Show, createSignal, onMount, onCleanup } from 'solid-js';
import { BookOpen } from 'lucide-solid';
import { Loading } from '../ui';
import PaperCard from './PaperCard';

const PaperGrid = (props) => {

  // 响应式瀑布流状态
  const [columns, setColumns] = createSignal(3);
  let containerRef;



  // 计算最优列数
  const calculateOptimalColumns = (containerWidth) => {

    // 设置最小卡片宽度和间隙
    const minCardWidth = 260; // 最小卡片宽度
    const gap = 16; // 间隙

    // 计算每列实际需要的宽度（卡片宽度 + 间隙）
    const calculateRequiredWidth = (cols) => {
      return cols * minCardWidth + (cols - 1) * gap;
    };

    // 从最大列数开始尝试（最大4列）
    for (let cols = 4; cols >= 1; cols--) {
      const requiredWidth = calculateRequiredWidth(cols);

      if (requiredWidth <= containerWidth) {
        return cols;
      }
    }
    
    return 1;
  };

  // 分配卡片到各列
  const distributeCards = (papers, columnCount) => {
    const columns = Array.from({ length: columnCount }, () => []);
    const columnHeights = Array(columnCount).fill(0);

    papers.forEach((paper, paperIndex) => {
      // 估算卡片高度（基于内容长度）
      const estimatedHeight = 200 + // 基础高度
        (paper.title?.length || 0) * 0.8 + // 标题长度
        (paper.abstract_text?.length || 0) * 0.3 + // 摘要长度
        (paper.keywords?.length || 0) * 20; // 关键词数量

      // 找到最短的列，如果有多个相同高度的列，优先选择左边的（索引小的）
      let shortestColumnIndex = 0;
      let minHeight = columnHeights[0];

      for (let i = 1; i < columnHeights.length; i++) {
        // 只有当右边的列明显更短时（差距超过50px）才选择右边的列
        if (columnHeights[i] < minHeight - 5) {
          minHeight = columnHeights[i];
          shortestColumnIndex = i;
        }
      }

      // 将卡片分配到选中的列
      columns[shortestColumnIndex].push(paper);
      columnHeights[shortestColumnIndex] += estimatedHeight;

      // 调试信息
    });

    return columns;
  };

  // 更新列数
  const updateColumns = () => {
    if (!containerRef) return;

    const width = containerRef.offsetWidth || containerRef.clientWidth;
    const newColumns = calculateOptimalColumns(width);

    if (newColumns !== columns()) {
      setColumns(newColumns);
    }
  };

  // 防抖的更新函数
  let updateTimeout;
  const debouncedUpdate = () => {
    clearTimeout(updateTimeout);
    updateTimeout = setTimeout(updateColumns, 100);
  };

  // 初始化
  onMount(() => {
    // 初始计算
    setTimeout(updateColumns, 100);

    // 窗口 resize 监听
    window.addEventListener('resize', debouncedUpdate);

    // ResizeObserver 监听容器变化
    if (containerRef && window.ResizeObserver) {
      const observer = new ResizeObserver(debouncedUpdate);
      observer.observe(containerRef);

      onCleanup(() => {
        observer.disconnect();
      });
    }
  });

  // 清理
  onCleanup(() => {
    window.removeEventListener('resize', debouncedUpdate);
    clearTimeout(updateTimeout);
  });

  return (
    <>
      {/* 加载状态 */}
      <Show when={props.loading}>
        <div class="flex items-center justify-center h-64">
          <Loading
            size="lg"
            variant="spinner"
            text="正在加载文献列表..."
            class="text-theme-accent"
          />
        </div>
      </Show>

      {/* 错误状态 */}
      <Show when={props.error && !props.loading}>
        <div class="text-center py-12">
          <div class="w-16 h-16 bg-theme-error rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-theme-primary mb-2">加载失败</h3>
          <p class="text-theme-muted mb-4">{props.error}</p>
          <button
            onClick={props.onRetry}
            class="px-4 py-2 bg-theme-accent text-white rounded-lg hover:bg-theme-accent-hover transition-colors"
          >
            重试
          </button>
        </div>
      </Show>

      {/* 空状态 */}
      <Show when={!props.loading && !props.error && (!Array.isArray(props.papers) || props.papers.length === 0)}>
        <div class="text-center py-12">
          <BookOpen size={48} class="mx-auto text-theme-muted mb-4" />
          <h3 class="text-lg font-medium text-theme-primary mb-2">暂无文献</h3>
          <p class="text-theme-muted">点击右上角的"添加文献"按钮开始添加</p>
        </div>
      </Show>

      {/* 响应式瀑布流网格布局 */}
      <Show when={!props.loading && !props.error && Array.isArray(props.papers) && props.papers.length > 0}>
        <div
          ref={containerRef}
          class="flex gap-4"
          style={{
            'min-height': '200px'
          }}
        >
          <For each={distributeCards(props.papers, columns())}>
            {(columnPapers) => (
              <div class="flex-1 flex flex-col gap-4">
                <For each={columnPapers}>
                  {(paper) => (
                    <PaperCard
                      paper={paper}
                      isSelected={props.selectedPapers.has(paper.id)}
                      selectMode={props.selectMode}
                      onSelect={props.onPaperSelect}
                      onToggleSelection={props.onToggleSelection}
                    />
                  )}
                </For>
              </div>
            )}
          </For>
        </div>
      </Show>
    </>
  );
};

export default PaperGrid;
