import { createSignal } from 'solid-js';
import Select from './Select';

// 使用示例组件
const SelectExample = () => {
  const [singleValue, setSingleValue] = createSignal('');
  const [multipleValues, setMultipleValues] = createSignal([]);
  const [searchableValue, setSearchableValue] = createSignal('');

  // 基础选项数据
  const basicOptions = [
    { value: 'option1', label: '选项 1' },
    { value: 'option2', label: '选项 2' },
    { value: 'option3', label: '选项 3' },
    { value: 'option4', label: '选项 4' }
  ];

  // 分组选项数据
  const groupedOptions = [
    { value: 'apple', label: '苹果', category: '水果' },
    { value: 'banana', label: '香蕉', category: '水果' },
    { value: 'orange', label: '橙子', category: '水果' },
    { value: 'carrot', label: '胡萝卜', category: '蔬菜' },
    { value: 'broccoli', label: '西兰花', category: '蔬菜' },
    { value: 'spinach', label: '菠菜', category: '蔬菜' }
  ];

  // 简单字符串选项
  const simpleOptions = ['北京', '上海', '广州', '深圳', '杭州', '南京'];

  return (
    <div class="p-6 space-y-8 max-w-2xl">
      <h1 class="text-2xl font-bold text-theme-primary">Select 组件示例</h1>

      {/* 基础单选 */}
      <div>
        <h2 class="text-lg font-semibold mb-4">基础单选</h2>
        <Select
          label="选择一个选项"
          placeholder="请选择..."
          options={basicOptions}
          value={singleValue()}
          onSelectionChange={setSingleValue}
          helperText="这是一个基础的单选下拉框"
        />
        <p class="mt-2 text-sm text-theme-muted">
          选中值: {singleValue() || '未选择'}
        </p>
      </div>

      {/* 多选 */}
      <div>
        <h2 class="text-lg font-semibold mb-4">多选模式</h2>
        <Select
          label="选择多个选项"
          placeholder="请选择多个选项..."
          options={basicOptions}
          value={multipleValues()}
          onSelectionChange={setMultipleValues}
          multiple
          clearable
          helperText="可以选择多个选项"
        />
        <p class="mt-2 text-sm text-theme-muted">
          选中值: {JSON.stringify(multipleValues())}
        </p>
      </div>

      {/* 可搜索 */}
      <div>
        <h2 class="text-lg font-semibold mb-4">可搜索选择</h2>
        <Select
          label="搜索城市"
          placeholder="输入城市名称搜索..."
          options={simpleOptions}
          value={searchableValue()}
          onSelectionChange={setSearchableValue}
          searchable
          clearable
          helperText="支持搜索过滤选项"
        />
        <p class="mt-2 text-sm text-theme-muted">
          选中值: {searchableValue() || '未选择'}
        </p>
      </div>

      {/* 分组选项 */}
      <div>
        <h2 class="text-lg font-semibold mb-4">分组选项</h2>
        <Select
          label="选择食物"
          placeholder="请选择食物..."
          options={groupedOptions}
          value={singleValue()}
          onSelectionChange={setSingleValue}
          groupBy={(option) => option.category}
          searchable
          clearable
          helperText="选项按类别分组显示"
        />
      </div>

      {/* 不同尺寸 */}
      <div>
        <h2 class="text-lg font-semibold mb-4">不同尺寸</h2>
        <div class="space-y-4">
          <Select
            label="小尺寸"
            size="sm"
            options={basicOptions}
            placeholder="小尺寸选择框"
          />
          <Select
            label="中等尺寸（默认）"
            size="md"
            options={basicOptions}
            placeholder="中等尺寸选择框"
          />
          <Select
            label="大尺寸"
            size="lg"
            options={basicOptions}
            placeholder="大尺寸选择框"
          />
        </div>
      </div>

      {/* 错误状态 */}
      <div>
        <h2 class="text-lg font-semibold mb-4">错误状态</h2>
        <Select
          label="必填选项"
          options={basicOptions}
          placeholder="请选择..."
          error="此字段为必填项"
        />
      </div>

      {/* 禁用状态 */}
      <div>
        <h2 class="text-lg font-semibold mb-4">禁用状态</h2>
        <Select
          label="禁用的选择框"
          options={basicOptions}
          placeholder="已禁用"
          disabled
          helperText="此选择框已被禁用"
        />
      </div>

      {/* 自定义渲染 */}
      <div>
        <h2 class="text-lg font-semibold mb-4">自定义渲染</h2>
        <Select
          label="自定义选项渲染"
          options={groupedOptions}
          placeholder="选择食物..."
          renderOption={(option, isSelected) => (
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium">{option.label}</div>
                <div class="text-xs text-theme-muted">{option.category}</div>
              </div>
              {isSelected && <span class="text-theme-accent">✓</span>}
            </div>
          )}
          renderValue={(option) => (
            <span>
              {option?.label} <span class="text-theme-muted">({option?.category})</span>
            </span>
          )}
          searchable
          clearable
        />
      </div>
    </div>
  );
};

export default SelectExample;
