# Lite Papers API 文档说明

## 📚 文档概览

本项目提供了完整的API文档，帮助开发者快速理解和使用Lite Papers系统。

### 📄 文档文件

1. **[API_DOCUMENTATION.md](./API_DOCUMENTATION.md)** - 完整详细的API文档
   - 包含所有端点的详细说明
   - 请求/响应示例
   - 数据模型定义
   - 使用示例和最佳实践

2. **[API_QUICK_REFERENCE.md](./API_QUICK_REFERENCE.md)** - 快速参考手册
   - 简洁的端点列表
   - 常用参数说明
   - 快速示例代码

## 🚀 快速开始

### 1. 启动服务
```bash
cargo run
```

服务将在 `http://127.0.0.1:3000` 启动

### 2. 健康检查
```bash
curl http://127.0.0.1:3000/health
```

### 3. 获取文献列表
```bash
curl "http://127.0.0.1:3000/api/papers?page=1&page_size=10"
```

### 4. 搜索文献
```bash
curl "http://127.0.0.1:3000/api/papers/search?q=transformer"
```

## 📋 核心功能

### 文献管理
- ✅ 创建、读取、更新、删除文献
- ✅ 分页查询
- ✅ 文献元数据管理

### 搜索功能
- ✅ 全文搜索
- ✅ 多条件过滤
- ✅ 排序和分页
- ✅ 年份范围查询

### 文件管理
- ✅ 文件上传（PDF、图片、笔记）
- ✅ 文件下载和预览
- ✅ 文件类型管理

### 文件夹管理
- ✅ 文件夹创建和删除
- ✅ 文件夹重命名
- ✅ 层级结构支持

### 统计分析
- ✅ 文献统计信息
- ✅ 文件夹树结构
- ✅ 热门作者和关键词

## 🛠️ 使用工具

### Postman
1. 导入 `Lite_Papers_API.postman_collection.json`
2. 设置环境变量 `baseUrl` 为 `http://127.0.0.1:3000`
3. 开始测试API

### cURL
参考文档中的示例命令，或使用快速参考手册中的命令。

### 编程语言客户端
可以使用任何支持HTTP请求的编程语言：
- JavaScript/Node.js (fetch, axios)
- Python (requests)
- Java (HttpClient)
- C# (HttpClient)
- Go (net/http)

## 📊 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "Success",
  "data": { ... }
}
```

### 分页响应
```json
{
  "papers": [...],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total": 100,
    "has_next": true,
    "has_prev": false
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述"
}
```

## 🔧 配置说明

系统配置文件：`config.toml`

主要配置项：
- 服务器地址和端口
- 分页参数限制
- 支持的文件格式
- 搜索参数配置

## 📈 性能特性

- **内存缓存**: 所有文献数据缓存在内存中，查询速度极快
- **全文索引**: 支持标题、摘要、关键词的全文搜索
- **分页优化**: 高效的分页实现
- **性能监控**: 每个请求都包含响应时间信息

## 🔍 测试验证

API文档已通过实际测试验证：
- ✅ 健康检查端点正常
- ✅ 文献列表查询正常
- ✅ 搜索功能正常
- ✅ 分页功能正常
- ✅ 响应格式正确

## 📞 技术支持

如果在使用API过程中遇到问题：

1. 查看完整的API文档了解详细信息
2. 使用Postman集合进行测试
3. 检查服务器日志获取错误信息
4. 确认请求格式和参数正确性

## 🎯 最佳实践

1. **分页查询**: 使用合适的页面大小（10-50）
2. **搜索优化**: 结合多个过滤条件提高查询精度
3. **错误处理**: 检查HTTP状态码和错误信息
4. **性能监控**: 关注响应时间头信息
5. **文件管理**: 使用有意义的文件名和正确的文件类型

---

**注意**: 本API文档基于当前版本的实现，如有更新请及时查看最新文档。
