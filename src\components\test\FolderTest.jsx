import { createSignal, onMount } from 'solid-js';
import { Folder, FolderOpen, ChevronRight, ChevronDown } from 'lucide-solid';

const FolderTest = () => {
  const [folders, setFolders] = createSignal([]);
  const [loading, setLoading] = createSignal(true);

  onMount(() => {
    // 直接设置模拟数据
    setTimeout(() => {
      setFolders([
        {
          name: 'AI Papers',
          path: 'AI Papers',
          papers_count: 25,
          children: [
            { name: 'Machine Learning', path: 'AI Papers/Machine Learning', papers_count: 15, children: [] },
            { name: 'Deep Learning', path: 'AI Papers/Deep Learning', papers_count: 10, children: [] }
          ]
        },
        {
          name: 'Computer Vision',
          path: 'Computer Vision',
          papers_count: 18,
          children: []
        },
        {
          name: 'NLP',
          path: 'NLP',
          papers_count: 12,
          children: []
        }
      ]);
      setLoading(false);
    }, 100);
  });

  const renderFolder = (folder) => {
    return (
      <div key={folder.path} class="p-2 border border-gray-200 rounded mb-2">
        <div class="flex items-center space-x-2">
          <Folder size={16} class="text-gray-500" />
          <span class="text-sm font-medium">{folder.name}</span>
          <span class="text-xs text-gray-500">({folder.papers_count})</span>
        </div>
        {folder.children && folder.children.length > 0 && (
          <div class="ml-4 mt-2">
            {folder.children.map(child => renderFolder(child))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div class="p-4">
      <h2 class="text-lg font-bold mb-4">文件夹测试</h2>
      
      {loading() ? (
        <div class="text-center">
          <div class="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
          <p>加载中...</p>
        </div>
      ) : (
        <div>
          <p class="mb-4">文件夹数量: {folders().length}</p>
          <div>
            {folders().map(folder => renderFolder(folder))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FolderTest;
