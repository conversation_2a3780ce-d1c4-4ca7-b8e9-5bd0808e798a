import { createContext, useContext, createSignal, createEffect, onMount } from 'solid-js';

// 创建主题上下文
const ThemeContext = createContext();

// 主题类型
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto'
};

// 获取初始主题
const getInitialTheme = () => {
  // 1. 检查 localStorage 中的用户偏好
  if (typeof window !== 'undefined') {
    const saved = localStorage.getItem('theme');
    if (saved && Object.values(THEMES).includes(saved)) {
      return saved;
    }
  }
  
  // 2. 默认跟随系统
  return THEMES.AUTO;
};

// 获取系统主题偏好
const getSystemTheme = () => {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? THEMES.DARK : THEMES.LIGHT;
  }
  return THEMES.LIGHT;
};

// 计算实际应用的主题
const getEffectiveTheme = (theme) => {
  if (theme === THEMES.AUTO) {
    return getSystemTheme();
  }
  return theme;
};

// 更新文档的主题属性
const updateDocumentTheme = (theme) => {
  if (typeof document !== 'undefined') {
    const effectiveTheme = getEffectiveTheme(theme);
    document.documentElement.setAttribute('data-theme', effectiveTheme);
    
    // 更新 meta theme-color
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', effectiveTheme === THEMES.DARK ? '#1f2937' : '#ffffff');
    }
  }
};

// 主题提供者组件
export const ThemeProvider = (props) => {
  const [theme, setTheme] = createSignal(getInitialTheme());
  const [systemTheme, setSystemTheme] = createSignal(getSystemTheme());

  // 监听系统主题变化
  onMount(() => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleChange = (e) => {
        setSystemTheme(e.matches ? THEMES.DARK : THEMES.LIGHT);
      };
      
      // 添加监听器
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', handleChange);
      } else {
        // 兼容旧版浏览器
        mediaQuery.addListener(handleChange);
      }
      
      // 清理函数
      return () => {
        if (mediaQuery.removeEventListener) {
          mediaQuery.removeEventListener('change', handleChange);
        } else {
          mediaQuery.removeListener(handleChange);
        }
      };
    }
  });

  // 当主题或系统主题变化时更新文档
  createEffect(() => {
    updateDocumentTheme(theme());
  });

  createEffect(() => {
    // 当系统主题变化且用户选择了自动模式时，重新应用主题
    if (theme() === THEMES.AUTO) {
      updateDocumentTheme(theme());
    }
  });

  // 设置主题并保存到 localStorage
  const changeTheme = (newTheme) => {
    setTheme(newTheme);
    if (typeof window !== 'undefined') {
      localStorage.setItem('theme', newTheme);
    }
  };

  // 切换主题（在 light/dark 之间切换）
  const toggleTheme = () => {
    const currentEffective = getEffectiveTheme(theme());
    const newTheme = currentEffective === THEMES.DARK ? THEMES.LIGHT : THEMES.DARK;
    changeTheme(newTheme);
  };

  // 获取当前生效的主题
  const getEffectiveCurrentTheme = () => {
    return getEffectiveTheme(theme());
  };

  // 检查是否为深色主题
  const isDark = () => {
    return getEffectiveCurrentTheme() === THEMES.DARK;
  };

  // 检查是否为浅色主题
  const isLight = () => {
    return getEffectiveCurrentTheme() === THEMES.LIGHT;
  };

  // 检查是否为自动模式
  const isAuto = () => {
    return theme() === THEMES.AUTO;
  };

  // 初始化文档主题
  onMount(() => {
    updateDocumentTheme(theme());
  });

  const contextValue = {
    // 状态
    theme,
    systemTheme,
    
    // 计算属性
    effectiveTheme: getEffectiveCurrentTheme,
    isDark,
    isLight,
    isAuto,
    
    // 方法
    setTheme: changeTheme,
    toggleTheme,
    
    // 常量
    THEMES
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {props.children}
    </ThemeContext.Provider>
  );
};

// 使用主题的 Hook
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// 主题工具函数
export const themeUtils = {
  // 获取主题相关的类名
  getThemeClass: (baseClass, themeClass) => {
    return `${baseClass} ${themeClass}`;
  },
  
  // 根据主题返回不同的值
  themeValue: (lightValue, darkValue, currentTheme) => {
    return currentTheme === THEMES.DARK ? darkValue : lightValue;
  },
  
  // 获取主题相关的样式
  getThemeStyle: (property, lightValue, darkValue, currentTheme) => {
    const value = currentTheme === THEMES.DARK ? darkValue : lightValue;
    return { [property]: value };
  }
};

export default ThemeContext;
