import { createSignal, For, splitProps } from 'solid-js';
import { Upload, X, File, Image, FileText } from 'lucide-solid';

const FileUpload = (props) => {
  const [local, others] = splitProps(props, [
    'multiple',
    'accept',
    'maxSize',
    'maxFiles',
    'onFileSelect',
    'onFileRemove',
    'disabled',
    'dragAndDrop',
    'showPreview',
    'class'
  ]);

  const [files, setFiles] = createSignal([]);
  const [isDragging, setIsDragging] = createSignal(false);
  let fileInputRef;

  const maxSize = local.maxSize || 10 * 1024 * 1024; // 10MB
  const maxFiles = local.maxFiles || 10;

  // 文件类型图标
  const getFileIcon = (file) => {
    if (file.type.startsWith('image/')) {
      return <Image size={20} class="text-theme-success" />;
    } else if (file.type === 'application/pdf' || file.type.startsWith('text/')) {
      return <FileText size={20} class="text-theme-info" />;
    }
    return <File size={20} class="text-theme-muted" />;
  };

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 验证文件
  const validateFile = (file) => {
    if (file.size > maxSize) {
      return `文件大小不能超过 ${formatFileSize(maxSize)}`;
    }
    return null;
  };

  // 处理文件选择
  const handleFileSelect = (selectedFiles) => {
    const fileArray = Array.from(selectedFiles);
    const validFiles = [];
    const errors = [];

    fileArray.forEach(file => {
      const error = validateFile(file);
      if (error) {
        errors.push(`${file.name}: ${error}`);
      } else {
        validFiles.push({
          file,
          id: Date.now() + Math.random(),
          name: file.name,
          size: file.size,
          type: file.type,
          preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : null
        });
      }
    });

    if (errors.length > 0) {
      console.error('文件验证失败:', errors);
      // 这里可以显示错误提示
    }

    const currentFiles = files();
    const newFiles = [...currentFiles, ...validFiles].slice(0, maxFiles);
    setFiles(newFiles);

    if (local.onFileSelect) {
      local.onFileSelect(newFiles);
    }
  };

  // 移除文件
  const removeFile = (fileId) => {
    const newFiles = files().filter(f => f.id !== fileId);
    setFiles(newFiles);
    
    if (local.onFileRemove) {
      local.onFileRemove(newFiles);
    }
  };

  // 拖拽处理
  const handleDragOver = (e) => {
    e.preventDefault();
    if (!local.disabled && local.dragAndDrop !== false) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (!local.disabled && local.dragAndDrop !== false) {
      const droppedFiles = e.dataTransfer.files;
      handleFileSelect(droppedFiles);
    }
  };

  // 点击上传
  const handleClick = () => {
    if (!local.disabled && fileInputRef) {
      fileInputRef.click();
    }
  };

  const handleInputChange = (e) => {
    if (e.target.files) {
      handleFileSelect(e.target.files);
    }
  };

  return (
    <div class={`space-y-4 ${local.class || ''}`} {...others}>
      {/* 上传区域 */}
      <div
        class={`
          border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer
          ${isDragging()
            ? 'border-theme-accent bg-theme-accent'
            : 'border-theme-secondary hover:border-theme-accent'
          }
          ${local.disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <Upload size={32} class="mx-auto text-theme-muted mb-2" />
        <p class="text-sm text-theme-secondary mb-1">
          {local.dragAndDrop !== false ? '拖拽文件到此处或点击上传' : '点击选择文件'}
        </p>
        <p class="text-xs text-theme-muted">
          最大 {formatFileSize(maxSize)}，最多 {maxFiles} 个文件
        </p>
        
        <input
          ref={fileInputRef}
          type="file"
          multiple={local.multiple}
          accept={local.accept}
          onChange={handleInputChange}
          class="hidden"
          disabled={local.disabled}
        />
      </div>

      {/* 文件列表 */}
      {files().length > 0 && (
        <div class="space-y-2">
          <h4 class="text-sm font-medium text-theme-primary">已选择的文件</h4>
          <For each={files()}>
            {(fileItem) => (
              <div class="flex items-center justify-between p-3 bg-theme-muted rounded-lg">
                <div class="flex items-center space-x-3">
                  {local.showPreview && fileItem.preview ? (
                    <img
                      src={fileItem.preview}
                      alt={fileItem.name}
                      class="w-10 h-10 object-cover rounded"
                    />
                  ) : (
                    getFileIcon(fileItem.file)
                  )}
                  <div>
                    <p class="text-sm font-medium text-theme-primary truncate max-w-48">
                      {fileItem.name}
                    </p>
                    <p class="text-xs text-theme-muted">
                      {formatFileSize(fileItem.size)}
                    </p>
                  </div>
                </div>

                <button
                  onClick={() => removeFile(fileItem.id)}
                  class="p-1 text-theme-muted hover:text-theme-error transition-colors"
                >
                  <X size={16} />
                </button>
              </div>
            )}
          </For>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
