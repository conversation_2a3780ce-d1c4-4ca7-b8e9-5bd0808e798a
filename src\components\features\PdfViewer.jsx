import * as pdfjsLib from 'pdfjs-dist';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker?url';
import { usePDFSlick, ScrollMode } from "@pdfslick/solid";
import "@pdfslick/solid/dist/pdf_viewer.css";
import { createEffect, onCleanup } from 'solid-js';
import PDFToolbar from './PDFToolbar';

// 在开发环境下导入测试工具
if (import.meta.env.DEV) {
  import('../../utils/testPdfFunctionality.js');
}

pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

export default function PDFViewer(props) {
  const {
    viewerRef,
    pdfSlickStore: store,
    PDFSlickViewer,
  } = usePDFSlick(props.pdfUrl, {
    scaleValue: "page-fit",
    textLayerMode: 2, // 启用文本层
    annotationMode: 2, // 启用注释模式
    annotationEditorMode: 0, // 强制设置为NONE，确保文本选择正常
  });

  // 配置PDFSlick并强制重置注释编辑器模式
  createEffect(() => {
    if (store.pdfSlick) {
      store.pdfSlick.setScrollMode(ScrollMode.WRAPPED);
    }
  });



  return (
    <div class='flex flex-col h-full'>
      <PDFToolbar
        store={store}
        paperId={props.paperId}
        originalFileName={props.originalFileName}
        paperTitle={props.paperTitle}
        onClose={props.onClose}
      />
      <div class='relative h-full'>
        <PDFSlickViewer {...{ store, viewerRef }} />
      </div>
    </div>
  );
}
