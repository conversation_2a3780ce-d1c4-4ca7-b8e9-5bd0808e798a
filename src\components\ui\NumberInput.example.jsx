import { createSignal } from 'solid-js';
import { Calendar, DollarSign, Percent, Hash } from 'lucide-solid';
import NumberInput from './NumberInput';

// 使用示例组件
const NumberInputExample = () => {
  const [basicValue, setBasicValue] = createSignal(0);
  const [yearValue, setYearValue] = createSignal(2024);
  const [priceValue, setPriceValue] = createSignal(99.99);
  const [percentValue, setPercentValue] = createSignal(50);
  const [countValue, setCountValue] = createSignal(1);
  const [precisionValue, setPrecisionValue] = createSignal(3.14159);

  // 自定义格式化函数
  const formatCurrency = (value) => {
    if (value === null || value === undefined) return '';
    return `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const parseCurrency = (str) => {
    const cleaned = str.replace(/[¥,\s]/g, '');
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? null : parsed;
  };

  const formatPercent = (value) => {
    if (value === null || value === undefined) return '';
    return `${value}%`;
  };

  const parsePercent = (str) => {
    const cleaned = str.replace(/[%\s]/g, '');
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? null : parsed;
  };

  return (
    <div class="p-6 space-y-8 max-w-2xl">
      <h1 class="text-2xl font-bold text-theme-primary">NumberInput 组件示例</h1>

      {/* 基础数字输入 */}
      <div>
        <h2 class="text-lg font-semibold mb-4">基础数字输入</h2>
        <NumberInput
          label="基础数字"
          value={basicValue()}
          onValueChange={setBasicValue}
          placeholder="请输入数字"
          helperText="支持键盘上下箭头调整数值"
        />
        <p class="mt-2 text-sm text-theme-muted">
          当前值: {basicValue()}
        </p>
      </div>

      {/* 年份输入 */}
      <div>
        <h2 class="text-lg font-semibold mb-4">年份输入</h2>
        <NumberInput
          label="发表年份"
          value={yearValue()}
          onValueChange={setYearValue}
          min={1900}
          max={2030}
          step={1}
          leftIcon={<Calendar size={16} />}
          placeholder="请输入年份"
          helperText="年份范围：1900-2030"
        />
        <p class="mt-2 text-sm text-theme-muted">
          当前值: {yearValue()}
        </p>
      </div>

      {/* 价格输入（带格式化） */}
      <div>
        <h2 class="text-lg font-semibold mb-4">价格输入（自定义格式化）</h2>
        <NumberInput
          label="商品价格"
          value={priceValue()}
          onValueChange={setPriceValue}
          min={0}
          max={999999}
          step={0.01}
          precision={2}
          formatter={formatCurrency}
          parser={parseCurrency}
          leftIcon={<DollarSign size={16} />}
          placeholder="请输入价格"
          helperText="自动格式化为货币格式"
        />
        <p class="mt-2 text-sm text-theme-muted">
          当前值: {priceValue()}
        </p>
      </div>

      {/* 百分比输入 */}
      <div>
        <h2 class="text-lg font-semibold mb-4">百分比输入</h2>
        <NumberInput
          label="完成进度"
          value={percentValue()}
          onValueChange={setPercentValue}
          min={0}
          max={100}
          step={5}
          formatter={formatPercent}
          parser={parsePercent}
          leftIcon={<Percent size={16} />}
          placeholder="请输入百分比"
          helperText="范围：0-100%，步长：5%"
        />
        <p class="mt-2 text-sm text-theme-muted">
          当前值: {percentValue()}
        </p>
      </div>

      {/* 计数器（无控制按钮） */}
      <div>
        <h2 class="text-lg font-semibold mb-4">计数器（隐藏控制按钮）</h2>
        <NumberInput
          label="商品数量"
          value={countValue()}
          onValueChange={setCountValue}
          min={1}
          max={999}
          step={1}
          showControls={false}
          leftIcon={<Hash size={16} />}
          placeholder="请输入数量"
          helperText="最小值：1，最大值：999"
        />
        <p class="mt-2 text-sm text-theme-muted">
          当前值: {countValue()}
        </p>
      </div>

      {/* 精度控制 */}
      <div>
        <h2 class="text-lg font-semibold mb-4">精度控制</h2>
        <NumberInput
          label="圆周率"
          value={precisionValue()}
          onValueChange={setPrecisionValue}
          step={0.001}
          precision={5}
          placeholder="请输入小数"
          helperText="保留5位小数"
        />
        <p class="mt-2 text-sm text-theme-muted">
          当前值: {precisionValue()}
        </p>
      </div>

      {/* 不同尺寸 */}
      <div>
        <h2 class="text-lg font-semibold mb-4">不同尺寸</h2>
        <div class="space-y-4">
          <NumberInput
            label="小尺寸"
            size="sm"
            value={10}
            placeholder="小尺寸数字输入"
          />
          <NumberInput
            label="中等尺寸（默认）"
            size="md"
            value={20}
            placeholder="中等尺寸数字输入"
          />
          <NumberInput
            label="大尺寸"
            size="lg"
            value={30}
            placeholder="大尺寸数字输入"
          />
        </div>
      </div>

      {/* 错误状态 */}
      <div>
        <h2 class="text-lg font-semibold mb-4">错误状态</h2>
        <NumberInput
          label="必填数字"
          value={null}
          min={1}
          max={100}
          placeholder="请输入1-100的数字"
          error="此字段为必填项，且必须在1-100范围内"
        />
      </div>

      {/* 禁用状态 */}
      <div>
        <h2 class="text-lg font-semibold mb-4">禁用状态</h2>
        <NumberInput
          label="禁用的数字输入"
          value={42}
          disabled
          helperText="此输入框已被禁用"
        />
      </div>

      {/* 允许空值 */}
      <div>
        <h2 class="text-lg font-semibold mb-4">允许空值</h2>
        <NumberInput
          label="可选数字"
          value={null}
          allowEmpty={true}
          placeholder="可以为空的数字输入"
          helperText="此字段可以为空"
        />
      </div>
    </div>
  );
};

export default NumberInputExample;
