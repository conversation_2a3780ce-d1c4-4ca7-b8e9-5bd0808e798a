import { defineConfig } from 'vite';
import solidPlugin from 'vite-plugin-solid';
import tailwind from '@tailwindcss/vite'

export default defineConfig({
  plugins: [solidPlugin(), tailwind()],
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:3000',
        changeOrigin: true,
      },
      '/health': {
        target: 'http://127.0.0.1:3000',
        changeOrigin: true,
      },
    },
  },
  build: {
    target: 'esnext',
  },
});
