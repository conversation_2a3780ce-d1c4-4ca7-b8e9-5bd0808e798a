import { createSignal, createMemo, splitProps, Show } from 'solid-js';
import { Plus, Minus } from 'lucide-solid';

const NumberInput = (props) => {
  const [local, others] = splitProps(props, [
    'value',
    'onValueChange',
    'min',
    'max',
    'step',
    'precision',
    'showControls',
    'allowEmpty',
    'formatter',
    'parser',
    'label',
    'error',
    'helperText',
    'leftIcon',
    'rightIcon',
    'size',
    'variant',
    'class',
    'containerClass',
    'disabled',
    'placeholder'
  ]);

  const [inputValue, setInputValue] = createSignal('');
  const [focused, setFocused] = createSignal(false);

  // 配置默认值
  const min = () => local.min ?? -Infinity;
  const max = () => local.max ?? Infinity;
  const step = () => local.step ?? 1;
  const precision = () => local.precision ?? 0;
  const showControls = () => local.showControls ?? true;
  const allowEmpty = () => local.allowEmpty ?? true;

  // 当前数值
  const currentValue = createMemo(() => {
    if (local.value === null || local.value === undefined) {
      return allowEmpty() ? null : min();
    }
    return Number(local.value);
  });

  // 显示值（格式化后的字符串）
  const displayValue = createMemo(() => {
    if (focused()) {
      return inputValue();
    }

    const value = currentValue();
    if (value === null || value === undefined) {
      return '';
    }

    if (local.formatter) {
      return local.formatter(value);
    }

    // 默认格式化：处理精度
    if (precision() > 0) {
      return value.toFixed(precision());
    }
    
    return value.toString();
  });

  // 解析输入值
  const parseValue = (str) => {
    if (!str || str.trim() === '') {
      return allowEmpty() ? null : min();
    }

    if (local.parser) {
      return local.parser(str);
    }

    // 默认解析：移除非数字字符（除了小数点和负号）
    const cleaned = str.replace(/[^\d.-]/g, '');
    const parsed = parseFloat(cleaned);
    
    return isNaN(parsed) ? null : parsed;
  };

  // 验证数值范围
  const clampValue = (value) => {
    if (value === null || value === undefined) {
      return allowEmpty() ? null : min();
    }
    
    const num = Number(value);
    if (isNaN(num)) {
      return allowEmpty() ? null : min();
    }

    return Math.max(min(), Math.min(max(), num));
  };

  // 更新值
  const updateValue = (newValue) => {
    const clampedValue = clampValue(newValue);
    if (clampedValue !== currentValue()) {
      local.onValueChange?.(clampedValue);
    }
  };

  // 处理输入变化
  const handleInput = (e) => {
    const value = e.target.value;
    setInputValue(value);
    
    // 实时解析和更新（可选）
    const parsed = parseValue(value);
    if (parsed !== null || allowEmpty()) {
      updateValue(parsed);
    }
  };

  // 处理焦点
  const handleFocus = (e) => {
    setFocused(true);
    setInputValue(currentValue()?.toString() || '');
    others.onFocus?.(e);
  };

  const handleBlur = (e) => {
    setFocused(false);
    const parsed = parseValue(inputValue());
    updateValue(parsed);
    others.onBlur?.(e);
  };

  // 处理键盘事件
  const handleKeyDown = (e) => {
    if (local.disabled) return;

    switch (e.key) {
      case 'ArrowUp':
        e.preventDefault();
        increment();
        break;
      case 'ArrowDown':
        e.preventDefault();
        decrement();
        break;
      case 'Enter':
        const parsed = parseValue(inputValue());
        updateValue(parsed);
        break;
    }
    
    others.onKeyDown?.(e);
  };

  // 增加数值
  const increment = () => {
    if (local.disabled) return;
    
    const current = currentValue() ?? 0;
    const newValue = current + step();
    updateValue(newValue);
  };

  // 减少数值
  const decrement = () => {
    if (local.disabled) return;
    
    const current = currentValue() ?? 0;
    const newValue = current - step();
    updateValue(newValue);
  };

  // 样式配置
  const baseClasses = 'block w-full rounded-lg border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-0 input-theme';
  
  const variants = {
    default: '',
    error: 'border-theme-error focus:border-theme-error focus:ring-theme-error',
    success: 'border-theme-success focus:border-theme-success focus:ring-theme-success'
  };

  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  };

  const variant = local.error ? 'error' : (local.variant || 'default');
  const size = local.size || 'md';

  const inputClasses = `${baseClasses} ${variants[variant]} ${sizes[size]} ${
    local.leftIcon ? 'pl-10' : ''
  } ${showControls() ? 'pr-16' : local.rightIcon ? 'pr-10' : ''} ${local.class || ''}`;

  // 控制按钮样式
  const controlButtonClasses = 'flex items-center justify-center w-6 h-6 text-theme-muted hover:text-theme-primary hover:bg-theme-muted rounded transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed';

  return (
    <div class={local.containerClass}>
      {local.label && (
        <label class="block text-sm font-medium text-theme-primary mb-1">
          {local.label}
        </label>
      )}
      
      <div class="relative">
        {/* 左侧图标 */}
        {local.leftIcon && (
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <div class="text-theme-muted">
              {local.leftIcon}
            </div>
          </div>
        )}
        
        {/* 输入框 */}
        <input
          type="text"
          inputmode="numeric"
          class={inputClasses}
          value={displayValue()}
          onInput={handleInput}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          disabled={local.disabled}
          placeholder={local.placeholder}
          {...others}
        />
        
        {/* 右侧图标（当没有控制按钮时） */}
        {!showControls() && local.rightIcon && (
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <div class="text-theme-muted">
              {local.rightIcon}
            </div>
          </div>
        )}

        {/* 数值控制按钮 */}
        {showControls() && (
          <div class="absolute inset-y-0 right-0 pr-2 flex items-center space-x-1">
            <div class="flex flex-col">
              <button
                type="button"
                onClick={increment}
                disabled={local.disabled || (currentValue() !== null && currentValue() >= max())}
                class={`${controlButtonClasses} h-4`}
                tabIndex={-1}
              >
                <Plus size={12} />
              </button>
              <button
                type="button"
                onClick={decrement}
                disabled={local.disabled || (currentValue() !== null && currentValue() <= min())}
                class={`${controlButtonClasses} h-4`}
                tabIndex={-1}
              >
                <Minus size={12} />
              </button>
            </div>
          </div>
        )}
      </div>
      
      {/* 错误信息 */}
      <Show when={local.error}>
        <p class="mt-1 text-sm text-theme-error">{local.error}</p>
      </Show>

      {/* 帮助文本 */}
      <Show when={local.helperText && !local.error}>
        <p class="mt-1 text-sm text-theme-muted">{local.helperText}</p>
      </Show>
    </div>
  );
};

export default NumberInput;
