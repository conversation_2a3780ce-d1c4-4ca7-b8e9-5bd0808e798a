/* 主题工具类 - 使用 CSS 变量的语义化类名 */

/* 背景色工具类 */
.bg-theme-primary { background-color: rgb(var(--bg-primary)); }
.bg-theme-secondary { background-color: rgb(var(--bg-secondary)); }
.bg-theme-elevated { background-color: rgb(var(--bg-elevated)); }
.bg-theme-muted { background-color: rgb(var(--bg-muted)); }
.bg-theme-accent { background-color: rgb(var(--bg-accent)); }

/* 文字色工具类 */
.text-theme-primary { color: rgb(var(--text-primary)); }
.text-theme-secondary { color: rgb(var(--text-secondary)); }
.text-theme-muted { color: rgb(var(--text-muted)); }
.text-theme-inverse { color: rgb(var(--text-inverse)); }
.text-theme-accent { color: rgb(var(--text-accent)); }

/* 边框色工具类 */
.border-theme-primary { border-color: rgb(var(--border-primary)); }
.border-theme-secondary { border-color: rgb(var(--border-secondary)); }
.border-theme-muted { border-color: rgb(var(--border-muted)); }
.border-theme-accent { border-color: rgb(var(--border-accent)); }

/* 状态色工具类 */
.text-theme-success { color: rgb(var(--success)); }
.bg-theme-success { background-color: rgb(var(--success-bg)); }
.border-theme-success { border-color: rgb(var(--success-border)); }

.text-theme-error { color: rgb(var(--error)); }
.bg-theme-error { background-color: rgb(var(--error-bg)); }
.border-theme-error { border-color: rgb(var(--error-border)); }

.text-theme-warning { color: rgb(var(--warning)); }
.bg-theme-warning { background-color: rgb(var(--warning-bg)); }
.border-theme-warning { border-color: rgb(var(--warning-border)); }

.text-theme-info { color: rgb(var(--info)); }
.bg-theme-info { background-color: rgb(var(--info-bg)); }
.border-theme-info { border-color: rgb(var(--info-border)); }

/* 主题色工具类 */
.text-theme-primary-color { color: rgb(var(--primary)); }
.bg-theme-primary-color { background-color: rgb(var(--primary)); }
.border-theme-primary-color { border-color: rgb(var(--primary)); }

.text-theme-secondary-color { color: rgb(var(--secondary)); }
.bg-theme-secondary-color { background-color: rgb(var(--secondary)); }
.border-theme-secondary-color { border-color: rgb(var(--secondary)); }

/* 悬浮状态工具类 */
.hover\:bg-theme-primary:hover { background-color: rgb(var(--bg-primary)); }
.hover\:bg-theme-secondary:hover { background-color: rgb(var(--bg-secondary)); }
.hover\:bg-theme-muted:hover { background-color: rgb(var(--bg-muted)); }

.hover\:text-theme-primary:hover { color: rgb(var(--text-primary)); }
.hover\:text-theme-accent:hover { color: rgb(var(--text-accent)); }

.hover\:border-theme-primary:hover { border-color: rgb(var(--border-primary)); }
.hover\:border-theme-accent:hover { border-color: rgb(var(--border-accent)); }

/* 焦点状态工具类 */
.focus\:border-theme-accent:focus { border-color: rgb(var(--border-accent)); }
.focus\:ring-theme-accent:focus { 
  --tw-ring-color: rgb(var(--border-accent) / 0.5);
  box-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}

/* 组合工具类 */
.card-theme {
  background-color: rgb(var(--bg-elevated));
  border-color: rgb(var(--border-primary));
  color: rgb(var(--text-primary));
}

.card-theme:hover {
  background-color: rgb(var(--bg-secondary));
  border-color: rgb(var(--border-secondary));
}

.input-theme {
  background-color: rgb(var(--input-bg));
  border-color: rgb(var(--input-border));
  color: rgb(var(--text-primary));
}

.input-theme:focus {
  border-color: rgb(var(--input-focus-border));
  --tw-ring-color: rgb(var(--input-focus-ring) / 0.5);
  box-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}

.btn-primary-theme {
  background-color: rgb(var(--btn-primary-bg));
  color: rgb(var(--btn-primary-text));
}

.btn-primary-theme:hover {
  background-color: rgb(var(--btn-primary-hover));
}

.btn-secondary-theme {
  background-color: rgb(var(--btn-secondary-bg));
  color: rgb(var(--btn-secondary-text));
}

.btn-secondary-theme:hover {
  background-color: rgb(var(--btn-secondary-hover));
}

.btn-outline-theme {
  background-color: transparent;
  border-color: rgb(var(--btn-outline-border));
  color: rgb(var(--text-primary));
}

.btn-outline-theme:hover {
  background-color: rgb(var(--btn-outline-hover));
}

.modal-theme {
  background-color: rgb(var(--modal-bg));
  border-color: rgb(var(--modal-border));
  color: rgb(var(--text-primary));
}

.modal-overlay-theme {
  background-color: rgb(var(--modal-overlay) / 0.5);
}

.sidebar-theme {
  background-color: rgb(var(--sidebar-bg));
  border-color: rgb(var(--sidebar-border));
}

.sidebar-item-theme {
  color: rgb(var(--text-secondary));
}

.sidebar-item-theme:hover {
  background-color: rgb(var(--sidebar-item-hover));
  color: rgb(var(--text-primary));
}

.sidebar-item-active-theme {
  background-color: rgb(var(--sidebar-item-active));
  color: rgb(var(--text-accent));
}

.header-theme {
  background-color: rgb(var(--header-bg));
  border-color: rgb(var(--header-border));
}

.paper-card-theme {
  background-color: rgb(var(--paper-bg));
  border-color: rgb(var(--paper-border));
  color: rgb(var(--text-primary));
}

.paper-card-theme:hover {
  background-color: rgb(var(--paper-hover-bg));
  border-color: rgb(var(--paper-hover-border));
}

/* 阴影工具类 */
.shadow-theme-sm {
  box-shadow: 0 1px 2px 0 rgb(var(--shadow) / 0.05);
}

.shadow-theme {
  box-shadow: 0 1px 3px 0 rgb(var(--shadow) / 0.1), 0 1px 2px -1px rgb(var(--shadow) / 0.1);
}

.shadow-theme-md {
  box-shadow: 0 4px 6px -1px rgb(var(--shadow) / 0.1), 0 2px 4px -2px rgb(var(--shadow) / 0.1);
}

.shadow-theme-lg {
  box-shadow: 0 10px 15px -3px rgb(var(--shadow) / 0.1), 0 4px 6px -4px rgb(var(--shadow) / 0.1);
}

/* 高亮工具类 */
.highlight-theme {
  background-color: rgb(var(--highlight));
  color: rgb(var(--text-primary));
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-weight: 500;
}

/* 分割线工具类 */
.divide-theme-y > :not([hidden]) ~ :not([hidden]) {
  border-top-color: rgb(var(--border-primary));
}

.divide-theme-x > :not([hidden]) ~ :not([hidden]) {
  border-left-color: rgb(var(--border-primary));
}

/* 响应式主题工具类 */
@media (prefers-color-scheme: dark) {
  .auto-theme {
    color-scheme: dark;
  }
}

/* 打印样式 */
@media print {
  * {
    background-color: white !important;
    color: black !important;
    border-color: #ccc !important;
  }
}
