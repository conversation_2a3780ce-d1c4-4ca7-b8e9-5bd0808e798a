import { SortAsc, SortDesc, FileText } from 'lucide-solid';
import { usePaperContext } from '../../stores/PaperContext';
import { getFolderDisplayName } from '../../utils/folderUtils';
import { Button, NumberInput, Select } from '../ui';

const PaperToolbar = (props) => {
  // 获取分页信息以显示文献数量
  const { pagination, filters } = usePaperContext();

  // 获取当前文件夹名称
  const getCurrentFolderName = () => {
    const folderPath = filters().folder;
    const displayName = getFolderDisplayName(folderPath);
    if (displayName === '根目录') {
      return '根目录';
    } else if (displayName) {
      return displayName;
    } else {
      return '全部文献'
    }
  };

  return (
    <div class="bg-theme-primary border-b border-theme-primary px-6 py-4">
      <div class="flex items-center justify-between">
        {/* 左侧：文献数量显示 */}
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <FileText size={16} class="text-theme-muted" />
            <span class="text-sm text-theme-secondary">
              {getCurrentFolderName()}
            </span>
            <span class="text-sm font-medium text-theme-primary">
              {pagination().total} 篇文献
            </span>
          </div>
        </div>

        {/* 右侧：年份筛选、排序和批量操作 */}
        <div class="flex items-center space-x-4">
          {/* 年份筛选 */}
          <div class="flex items-center space-x-2">
            <span class="text-sm text-theme-secondary">年份:</span>
            <NumberInput
              placeholder="起始年份"
              value={props.yearStart || null}
              onValueChange={props.onYearStartChange}
              size="sm"
              class="w-24"
              min={1900}
              max={2030}
              step={1}
              allowEmpty={true}
              showControls={false}
            />
            <span class="text-sm text-theme-muted">-</span>
            <NumberInput
              placeholder="结束年份"
              value={props.yearEnd || null}
              onValueChange={props.onYearEndChange}
              size="sm"
              class="w-24"
              min={1900}
              max={2030}
              step={1}
              allowEmpty={true}
              showControls={false}
            />
            {(props.yearStart || props.yearEnd) && (
              <Button
                onClick={props.onClearYearFilter}
                variant="ghost"
                size="sm"
              >
                清除
              </Button>
            )}
          </div>
          {/* 选择模式切换 */}
          <Button
            onClick={props.onSelectModeToggle}
            variant={props.selectMode ? "secondary" : "outline"}
            size="sm"
          >
            {props.selectMode ? '取消选择' : '批量选择'}
          </Button>

          {/* 排序 */}
          <div class="flex items-center space-x-2">
            <span class="text-sm text-theme-secondary">排序:</span>
            <Select
              value={props.sortBy}
              onSelectionChange={props.onSortByChange}
              options={[
                { value: 'created_at', label: '创建时间' },
                { value: 'updated_at', label: '更新时间' },
                { value: 'title', label: '标题' },
                { value: 'year', label: '年份' }
              ]}
              size="sm"
              class="w-32"
            />
            <Button
              onClick={props.onSortOrderToggle}
              variant="ghost"
              size="sm"
            >
              {props.sortOrder === 'asc' ? <SortAsc size={16} /> : <SortDesc size={16} />}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaperToolbar;
