import { createSignal, For, splitProps } from 'solid-js';
import { ChevronUp, ChevronDown, MoreVertical } from 'lucide-solid';
import Loading from './Loading';

const Table = (props) => {
  const [local, others] = splitProps(props, [
    'columns',
    'data',
    'loading',
    'sortable',
    'selectable',
    'onSort',
    'onSelect',
    'selectedRows',
    'rowKey',
    'class'
  ]);

  const [sortConfig, setSortConfig] = createSignal({ key: null, direction: 'asc' });

  const handleSort = (column) => {
    if (!local.sortable || !column.sortable) return;

    const currentSort = sortConfig();
    let direction = 'asc';
    
    if (currentSort.key === column.key && currentSort.direction === 'asc') {
      direction = 'desc';
    }

    setSortConfig({ key: column.key, direction });
    
    if (local.onSort) {
      local.onSort(column.key, direction);
    }
  };

  const handleSelectAll = (checked) => {
    if (!local.selectable || !local.onSelect) return;
    
    if (checked) {
      const allKeys = local.data.map(row => row[local.rowKey || 'id']);
      local.onSelect(allKeys);
    } else {
      local.onSelect([]);
    }
  };

  const handleSelectRow = (rowKey, checked) => {
    if (!local.selectable || !local.onSelect) return;
    
    const selectedRows = local.selectedRows || [];
    let newSelection;
    
    if (checked) {
      newSelection = [...selectedRows, rowKey];
    } else {
      newSelection = selectedRows.filter(key => key !== rowKey);
    }
    
    local.onSelect(newSelection);
  };

  const isAllSelected = () => {
    if (!local.data || !local.selectedRows) return false;
    return local.data.length > 0 && local.selectedRows.length === local.data.length;
  };

  const isIndeterminate = () => {
    if (!local.selectedRows) return false;
    return local.selectedRows.length > 0 && local.selectedRows.length < local.data.length;
  };

  return (
    <div class={`overflow-hidden ${local.class || ''}`} {...others}>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          {/* 表头 */}
          <thead class="bg-gray-50">
            <tr>
              {local.selectable && (
                <th class="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={isAllSelected()}
                    indeterminate={isIndeterminate()}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </th>
              )}
              
              <For each={local.columns}>
                {(column) => (
                  <th
                    class={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                      column.sortable && local.sortable ? 'cursor-pointer hover:bg-gray-100' : ''
                    }`}
                    onClick={() => handleSort(column)}
                  >
                    <div class="flex items-center space-x-1">
                      <span>{column.title}</span>
                      {column.sortable && local.sortable && (
                        <div class="flex flex-col">
                          <ChevronUp 
                            size={12} 
                            class={`${
                              sortConfig().key === column.key && sortConfig().direction === 'asc'
                                ? 'text-primary-600'
                                : 'text-gray-400'
                            }`}
                          />
                          <ChevronDown 
                            size={12} 
                            class={`-mt-1 ${
                              sortConfig().key === column.key && sortConfig().direction === 'desc'
                                ? 'text-primary-600'
                                : 'text-gray-400'
                            }`}
                          />
                        </div>
                      )}
                    </div>
                  </th>
                )}
              </For>
            </tr>
          </thead>

          {/* 表体 */}
          <tbody class="bg-white divide-y divide-gray-200">
            {local.loading ? (
              <tr>
                <td
                  colspan={local.columns.length + (local.selectable ? 1 : 0)}
                  class="px-6 py-12 text-center"
                >
                  <Loading
                    size="md"
                    text="加载中..."
                    class="text-primary-500"
                  />
                </td>
              </tr>
            ) : local.data && local.data.length > 0 ? (
              <For each={local.data}>
                {(row) => {
                  const rowKey = row[local.rowKey || 'id'];
                  const isSelected = local.selectedRows?.includes(rowKey);
                  
                  return (
                    <tr class={`hover:bg-gray-50 ${isSelected ? 'bg-primary-50' : ''}`}>
                      {local.selectable && (
                        <td class="px-6 py-4">
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={(e) => handleSelectRow(rowKey, e.target.checked)}
                            class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                          />
                        </td>
                      )}
                      
                      <For each={local.columns}>
                        {(column) => (
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {column.render 
                              ? column.render(row[column.key], row)
                              : row[column.key]
                            }
                          </td>
                        )}
                      </For>
                    </tr>
                  );
                }}
              </For>
            ) : (
              <tr>
                <td 
                  colspan={local.columns.length + (local.selectable ? 1 : 0)}
                  class="px-6 py-12 text-center text-gray-500"
                >
                  暂无数据
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Table;
