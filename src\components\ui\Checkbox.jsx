import { createSignal, splitProps } from 'solid-js';
import { Check, Minus } from 'lucide-solid';

const Checkbox = (props) => {
  const [local, others] = splitProps(props, [
    'checked',
    'indeterminate',
    'disabled',
    'label',
    'description',
    'size',
    'variant',
    'onChange',
    'class'
  ]);

  const size = local.size || 'md';
  const variant = local.variant || 'default';

  const sizes = {
    sm: {
      checkbox: 'w-4 h-4',
      icon: 12,
      label: 'text-sm',
      description: 'text-xs'
    },
    md: {
      checkbox: 'w-5 h-5',
      icon: 14,
      label: 'text-sm',
      description: 'text-xs'
    },
    lg: {
      checkbox: 'w-6 h-6',
      icon: 16,
      label: 'text-base',
      description: 'text-sm'
    }
  };

  const variants = {
    default: {
      unchecked: 'border-theme-secondary bg-theme-elevated',
      checked: 'border-theme-primary-color bg-theme-primary-color',
      indeterminate: 'border-theme-primary-color bg-theme-primary-color',
      disabled: 'border-theme-muted bg-theme-muted cursor-not-allowed'
    },
    success: {
      unchecked: 'border-theme-secondary bg-theme-elevated',
      checked: 'border-theme-success bg-theme-success',
      indeterminate: 'border-theme-success bg-theme-success',
      disabled: 'border-theme-muted bg-theme-muted cursor-not-allowed'
    },
    warning: {
      unchecked: 'border-theme-secondary bg-theme-elevated',
      checked: 'border-theme-warning bg-theme-warning',
      indeterminate: 'border-theme-warning bg-theme-warning',
      disabled: 'border-theme-muted bg-theme-muted cursor-not-allowed'
    },
    danger: {
      unchecked: 'border-theme-secondary bg-theme-elevated',
      checked: 'border-theme-error bg-theme-error',
      indeterminate: 'border-theme-error bg-theme-error',
      disabled: 'border-theme-muted bg-theme-muted cursor-not-allowed'
    }
  };

  const getCheckboxClasses = () => {
    const sizeClasses = sizes[size].checkbox;
    const variantConfig = variants[variant];
    
    let stateClasses;
    if (local.disabled) {
      stateClasses = variantConfig.disabled;
    } else if (local.indeterminate) {
      stateClasses = variantConfig.indeterminate;
    } else if (local.checked) {
      stateClasses = variantConfig.checked;
    } else {
      stateClasses = variantConfig.unchecked;
    }

    return `
      ${sizeClasses}
      ${stateClasses}
      border-2 rounded transition-all duration-200
      focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-theme-accent
      ${local.disabled ? '' : 'cursor-pointer hover:border-theme-accent'}
    `;
  };

  const handleChange = (e) => {
    if (!local.disabled && local.onChange) {
      local.onChange(e.target.checked, e);
    }
  };

  return (
    <div class={`flex items-start space-x-3 ${local.class || ''}`} {...others}>
      <div class="flex items-center h-5">
        <div class="relative">
          <input
            type="checkbox"
            checked={local.checked}
            disabled={local.disabled}
            onChange={handleChange}
            class="sr-only"
          />
          
          <div
            class={getCheckboxClasses()}
            onClick={(e) => {
              if (!local.disabled) {
                const syntheticEvent = {
                  target: { checked: !local.checked },
                  preventDefault: () => {},
                  stopPropagation: () => {}
                };
                handleChange(syntheticEvent);
              }
            }}
          >
            {/* 选中状态图标 */}
            {(local.checked || local.indeterminate) && (
              <div class="flex items-center justify-center w-full h-full">
                {local.indeterminate ? (
                  <Minus 
                    size={sizes[size].icon} 
                    class="text-white" 
                  />
                ) : (
                  <Check 
                    size={sizes[size].icon} 
                    class="text-white" 
                  />
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 标签和描述 */}
      {(local.label || local.description) && (
        <div class="flex-1">
          {local.label && (
            <label 
              class={`
                block font-medium
                ${sizes[size].label}
                ${local.disabled ? 'text-theme-muted' : 'text-theme-primary'}
                ${local.disabled ? 'cursor-not-allowed' : 'cursor-pointer'}
              `}
              onClick={(e) => {
                if (!local.disabled) {
                  const syntheticEvent = {
                    target: { checked: !local.checked },
                    preventDefault: () => {},
                    stopPropagation: () => {}
                  };
                  handleChange(syntheticEvent);
                }
              }}
            >
              {local.label}
            </label>
          )}
          
          {local.description && (
            <p class={`
              mt-1
              ${sizes[size].description}
              ${local.disabled ? 'text-theme-muted' : 'text-theme-secondary'}
            `}>
              {local.description}
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default Checkbox;
