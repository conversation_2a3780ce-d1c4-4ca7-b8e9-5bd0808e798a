import { createSignal } from 'solid-js';
import { HighlightText, Card, Input } from '../ui';

const HighlightTextTest = () => {
  const [searchQuery, setSearchQuery] = createSignal('attention transformer');

  const testTexts = {
    title: "Attention Is All You Need: A Comprehensive Study of Transformer Architecture",
    abstract: "In this paper, we propose the Transformer, a novel neural network architecture based solely on attention mechanisms, dispensing with recurrence and convolutions entirely. Experiments on two machine translation tasks show that these models are superior in quality while being more parallelizable and requiring significantly less time to train.",
    keywords: ["attention", "transformer", "neural networks", "machine learning", "deep learning", "NLP"]
  };

  return (
    <div class="p-8 space-y-6 bg-gray-50 min-h-screen">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">搜索高亮测试</h1>

        {/* 搜索输入框 */}
        <Card header={<h2 class="text-xl font-semibold">搜索查询</h2>}>
          <Input
            label="搜索关键词"
            placeholder="输入搜索关键词，用空格分隔多个词..."
            value={searchQuery()}
            onInput={(e) => setSearchQuery(e.target.value)}
            helperText={`当前搜索: ${searchQuery() || '无'}`}
          />
        </Card>

        {/* 标题高亮测试 */}
        <Card header={<h2 class="text-xl font-semibold">标题高亮</h2>}>
          <div class="space-y-4">
            <div>
              <h3 class="text-sm font-medium text-gray-700 mb-2">原始标题:</h3>
              <p class="text-gray-600">{testTexts.title}</p>
            </div>
            <div>
              <h3 class="text-sm font-medium text-gray-700 mb-2">高亮效果:</h3>
              <h3 class="text-lg font-bold text-gray-900">
                <HighlightText
                  text={testTexts.title}
                  searchQuery={searchQuery()}
                  highlightClass="bg-yellow-200 text-yellow-900 px-1 py-0.5 rounded font-bold"
                />
              </h3>
            </div>
          </div>
        </Card>

        {/* 摘要高亮测试 */}
        <Card header={<h2 class="text-xl font-semibold">摘要高亮</h2>}>
          <div class="space-y-4">
            <div>
              <h3 class="text-sm font-medium text-gray-700 mb-2">原始摘要:</h3>
              <p class="text-gray-600">{testTexts.abstract}</p>
            </div>
            <div>
              <h3 class="text-sm font-medium text-gray-700 mb-2">高亮效果:</h3>
              <p class="text-sm text-gray-600 leading-relaxed">
                <HighlightText
                  text={testTexts.abstract}
                  searchQuery={searchQuery()}
                  highlightClass="bg-yellow-200 text-yellow-900 px-1 py-0.5 rounded"
                />
              </p>
            </div>
          </div>
        </Card>

        {/* 关键词高亮测试 */}
        <Card header={<h2 class="text-xl font-semibold">关键词高亮</h2>}>
          <div class="space-y-4">
            <div>
              <h3 class="text-sm font-medium text-gray-700 mb-2">原始关键词:</h3>
              <div class="flex flex-wrap gap-2">
                {testTexts.keywords.map((keyword) => (
                  <span class="inline-flex items-center px-2 py-0.5 bg-gray-100 text-gray-700 text-xs font-medium rounded-md">
                    {keyword}
                  </span>
                ))}
              </div>
            </div>
            <div>
              <h3 class="text-sm font-medium text-gray-700 mb-2">高亮效果:</h3>
              <div class="flex flex-wrap gap-2">
                {testTexts.keywords.map((keyword) => (
                  <span class="inline-flex items-center px-2 py-0.5 bg-gray-100 text-gray-700 text-xs font-medium rounded-md">
                    <HighlightText
                      text={keyword}
                      searchQuery={searchQuery()}
                      highlightClass="bg-yellow-300 text-yellow-900 px-0.5 py-0.5 rounded font-medium"
                    />
                  </span>
                ))}
              </div>
            </div>
          </div>
        </Card>

        {/* 高亮选项测试 */}
        <Card header={<h2 class="text-xl font-semibold">高亮选项测试</h2>}>
          <div class="space-y-4">
            <div>
              <h3 class="text-sm font-medium text-gray-700 mb-2">大小写敏感:</h3>
              <p class="text-gray-600">
                <HighlightText
                  text="Attention is all you need, ATTENTION mechanisms are powerful"
                  searchQuery="attention"
                  caseSensitive={true}
                  highlightClass="bg-red-200 text-red-900 px-1 py-0.5 rounded"
                />
              </p>
            </div>
            <div>
              <h3 class="text-sm font-medium text-gray-700 mb-2">大小写不敏感 (默认):</h3>
              <p class="text-gray-600">
                <HighlightText
                  text="Attention is all you need, ATTENTION mechanisms are powerful"
                  searchQuery="attention"
                  caseSensitive={false}
                  highlightClass="bg-green-200 text-green-900 px-1 py-0.5 rounded"
                />
              </p>
            </div>
            <div>
              <h3 class="text-sm font-medium text-gray-700 mb-2">完整单词匹配:</h3>
              <p class="text-gray-600">
                <HighlightText
                  text="The attention mechanism and attentional processes"
                  searchQuery="attention"
                  wholeWord={true}
                  highlightClass="bg-blue-200 text-blue-900 px-1 py-0.5 rounded"
                />
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default HighlightTextTest;
