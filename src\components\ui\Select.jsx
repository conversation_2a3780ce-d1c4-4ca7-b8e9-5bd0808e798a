import { createSignal, createMemo, For, Show, onCleanup, splitProps } from 'solid-js';
import { ChevronDown, Check, X, Search } from 'lucide-solid';

const Select = (props) => {
  const [local, others] = splitProps(props, [
    'options',
    'value',
    'onSelectionChange',
    'placeholder',
    'label',
    'error',
    'helperText',
    'disabled',
    'multiple',
    'searchable',
    'clearable',
    'size',
    'variant',
    'class',
    'containerClass',
    'maxHeight',
    'renderOption',
    'renderValue',
    'groupBy',
    'dropdownDirection'
  ]);

  const [isOpen, setIsOpen] = createSignal(false);
  const [searchTerm, setSearchTerm] = createSignal('');
  const [focusedIndex, setFocusedIndex] = createSignal(-1);
  
  let selectRef;
  let searchInputRef;

  // 处理选中值
  const selectedValues = createMemo(() => {
    if (!local.value) return [];
    return Array.isArray(local.value) ? local.value : [local.value];
  });

  // 过滤选项
  const filteredOptions = createMemo(() => {
    if (!local.options) return [];
    
    let options = local.options;
    
    // 搜索过滤
    if (local.searchable && searchTerm()) {
      const term = searchTerm().toLowerCase();
      options = options.filter(option => 
        (option.label || option.value || option).toString().toLowerCase().includes(term)
      );
    }
    
    return options;
  });

  // 分组选项
  const groupedOptions = createMemo(() => {
    const options = filteredOptions();
    if (!local.groupBy) return [{ group: null, options }];
    
    const groups = {};
    options.forEach(option => {
      const groupKey = local.groupBy(option) || '其他';
      if (!groups[groupKey]) groups[groupKey] = [];
      groups[groupKey].push(option);
    });
    
    return Object.entries(groups).map(([group, options]) => ({ group, options }));
  });

  // 点击外部关闭
  const handleClickOutside = (event) => {
    if (selectRef && !selectRef.contains(event.target)) {
      setIsOpen(false);
      setSearchTerm('');
      setFocusedIndex(-1);
    }
  };

  document.addEventListener('click', handleClickOutside);
  onCleanup(() => {
    document.removeEventListener('click', handleClickOutside);
  });

  // 切换下拉状态
  const toggleOpen = () => {
    if (local.disabled) return;
    
    const newOpen = !isOpen();
    setIsOpen(newOpen);
    
    if (newOpen && local.searchable) {
      setTimeout(() => searchInputRef?.focus(), 0);
    }
    
    if (!newOpen) {
      setSearchTerm('');
      setFocusedIndex(-1);
    }
  };

  // 选择选项
  const selectOption = (option) => {
    const value = option.value !== undefined ? option.value : option;
    
    if (local.multiple) {
      const currentValues = selectedValues();
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];
      
      local.onSelectionChange?.(newValues);
    } else {
      local.onSelectionChange?.(value);
      setIsOpen(false);
      setSearchTerm('');
    }
  };

  // 清除选择
  const clearSelection = (e) => {
    e.stopPropagation();
    local.onSelectionChange?.(local.multiple ? [] : null);
  };

  // 移除单个选项（多选模式）
  const removeOption = (value, e) => {
    e.stopPropagation();
    if (local.multiple) {
      const newValues = selectedValues().filter(v => v !== value);
      local.onSelectionChange?.(newValues);
    }
  };

  // 键盘导航
  const handleKeyDown = (e) => {
    if (local.disabled) return;

    switch (e.key) {
      case 'Enter':
      case ' ':
        if (!isOpen()) {
          toggleOpen();
        } else if (focusedIndex() >= 0) {
          const allOptions = groupedOptions().flatMap(g => g.options);
          selectOption(allOptions[focusedIndex()]);
        }
        e.preventDefault();
        break;
      case 'Escape':
        setIsOpen(false);
        setSearchTerm('');
        break;
      case 'ArrowDown':
        if (!isOpen()) {
          toggleOpen();
        } else {
          const allOptions = groupedOptions().flatMap(g => g.options);
          setFocusedIndex(Math.min(focusedIndex() + 1, allOptions.length - 1));
        }
        e.preventDefault();
        break;
      case 'ArrowUp':
        if (isOpen()) {
          setFocusedIndex(Math.max(focusedIndex() - 1, 0));
          e.preventDefault();
        }
        break;
    }
  };

  // 样式配置
  const baseClasses = 'relative block w-full rounded-lg border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-0 input-theme cursor-pointer';
  
  const variants = {
    default: '',
    error: 'border-theme-error focus:border-theme-error focus:ring-theme-error',
    success: 'border-theme-success focus:border-theme-success focus:ring-theme-success'
  };

  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  };

  const variant = local.error ? 'error' : (local.variant || 'default');
  const size = local.size || 'md';

  // 渲染选中值
  const renderSelectedValue = () => {
    const values = selectedValues();
    if (values.length === 0) {
      return <span class="text-theme-muted">{local.placeholder || '请选择...'}</span>;
    }

    if (local.multiple) {
      if (values.length === 1) {
        const option = local.options?.find(opt => (opt.value !== undefined ? opt.value : opt) === values[0]);
        return local.renderValue ? local.renderValue(option) : (option?.label || option?.value || option);
      }
      return <span>{values.length} 项已选择</span>;
    } else {
      const option = local.options?.find(opt => (opt.value !== undefined ? opt.value : opt) === values[0]);
      return local.renderValue ? local.renderValue(option) : (option?.label || option?.value || option);
    }
  };

  const selectClasses = `${baseClasses} ${variants[variant]} ${sizes[size]} ${
    local.disabled ? 'opacity-50 cursor-not-allowed' : ''
  } ${local.class || ''}`;

  return (
    <div class={local.containerClass}>
      {local.label && (
        <label class="block text-sm font-medium text-theme-primary mb-1">
          {local.label}
        </label>
      )}
      
      <div 
        ref={selectRef}
        class="relative"
        onKeyDown={handleKeyDown}
        tabIndex={local.disabled ? -1 : 0}
      >
        {/* 选择框 */}
        <div
          class={selectClasses}
          onClick={toggleOpen}
          {...others}
        >
          <div class="flex items-center justify-between">
            <div class="flex-1 min-w-0">
              {renderSelectedValue()}
            </div>
            
            <div class="flex items-center space-x-1 ml-2">
              {local.clearable && selectedValues().length > 0 && (
                <button
                  type="button"
                  onClick={clearSelection}
                  class="text-theme-muted hover:text-theme-primary transition-colors"
                >
                  <X size={16} />
                </button>
              )}
              
              <ChevronDown 
                size={16} 
                class={`text-theme-muted transition-transform duration-200 ${
                  isOpen() ? 'transform rotate-180' : ''
                }`}
              />
            </div>
          </div>
        </div>

        {/* 下拉选项 */}
        <Show when={isOpen()}>
          <div class={`absolute z-50 w-full bg-white border border-theme-border rounded-lg shadow-lg ${
            local.dropdownDirection === 'up'
              ? 'bottom-full mb-1'
              : 'top-full mt-1'
          }`}>
            <div 
              class="max-h-60 overflow-auto py-1"
              style={{ 'max-height': local.maxHeight || '15rem' }}
            >
              {/* 搜索框 */}
              <Show when={local.searchable}>
                <div class="px-3 py-2 border-b border-theme-border">
                  <div class="relative">
                    <Search size={16} class="absolute left-3 top-1/2 transform -translate-y-1/2 text-theme-muted" />
                    <input
                      ref={searchInputRef}
                      type="text"
                      placeholder="搜索选项..."
                      value={searchTerm()}
                      onInput={(e) => setSearchTerm(e.target.value)}
                      class="w-full pl-9 pr-3 py-1.5 text-sm border border-theme-border rounded focus:outline-none focus:ring-1 focus:ring-theme-accent"
                    />
                  </div>
                </div>
              </Show>

              {/* 选项列表 */}
              <For each={groupedOptions()}>
                {(group) => (
                  <>
                    <Show when={group.group}>
                      <div class="px-3 py-1 text-xs font-medium text-theme-muted uppercase tracking-wider bg-theme-muted/10">
                        {group.group}
                      </div>
                    </Show>
                    
                    <For each={group.options}>
                      {(option, index) => {
                        const value = option.value !== undefined ? option.value : option;
                        const isSelected = selectedValues().includes(value);
                        const globalIndex = groupedOptions().slice(0, groupedOptions().indexOf(group))
                          .reduce((acc, g) => acc + g.options.length, 0) + index();
                        const isFocused = focusedIndex() === globalIndex;
                        
                        return (
                          <div
                            class={`px-3 py-2 cursor-pointer transition-colors duration-150 ${
                              isFocused ? 'bg-theme-muted' : ''
                            } ${isSelected ? 'bg-theme-accent/10' : 'hover:bg-theme-muted'}`}
                            onClick={() => selectOption(option)}
                          >
                            <div class="flex items-center justify-between">
                              <div class="flex-1">
                                {local.renderOption 
                                  ? local.renderOption(option, isSelected)
                                  : (option.label || option.value || option)
                                }
                              </div>
                              
                              <Show when={isSelected}>
                                <Check size={16} class="text-theme-accent" />
                              </Show>
                            </div>
                          </div>
                        );
                      }}
                    </For>
                  </>
                )}
              </For>

              {/* 无选项提示 */}
              <Show when={filteredOptions().length === 0}>
                <div class="px-3 py-2 text-sm text-theme-muted text-center">
                  {local.searchable && searchTerm() ? '未找到匹配选项' : '暂无选项'}
                </div>
              </Show>
            </div>
          </div>
        </Show>
      </div>
      
      {/* 错误信息 */}
      <Show when={local.error}>
        <p class="mt-1 text-sm text-theme-error">{local.error}</p>
      </Show>

      {/* 帮助文本 */}
      <Show when={local.helperText && !local.error}>
        <p class="mt-1 text-sm text-theme-muted">{local.helperText}</p>
      </Show>
    </div>
  );
};

export default Select;
