@import "tailwindcss";

/* 导入主题样式 */
@import "./styles/themes.css";
@import "./styles/theme-utilities.css";

/* 确保根元素高度设置正确，防止双滚动条 */
html, body {
  height: 100%;
  overflow: hidden;
}

#root {
  height: 100%;
  overflow: hidden;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto',
    'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',
    'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* 应用主题背景和文字色 */
  background-color: rgb(var(--bg-primary));
  color: rgb(var(--text-primary));
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.pdf-viewer-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pdf-toolbar {
  padding: 10px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-container input {
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.search-container button {
  padding: 5px 10px;
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.search-navigation {
  display: flex;
  align-items: center;
  gap: 5px;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.zoom-controls button {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.pdf-pages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f9f9f9;
}

.pdf-page {
  display: block;
  margin: 0 auto;
}

.pdf-status {
  padding: 10px;
  text-align: center;
  background: #f5f5f5;
  border-top: 1px solid #ddd;
}

.pdf-loading, .pdf-error {
  padding: 20px;
  text-align: center;
}

.pdf-error {
  color: #d32f2f;
}