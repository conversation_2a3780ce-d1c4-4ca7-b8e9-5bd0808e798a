{"name": "lite-papers-ui", "version": "1.0.0", "description": "A modern frontend for Lite Papers - lightweight research paper management system", "type": "module", "scripts": {"start": "vite", "dev": "vite", "build": "vite build", "serve": "vite preview", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css,md}"}, "license": "MIT", "devDependencies": {"@tailwindcss/forms": "^0.5.10", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "vite": "^6.0.0", "vite-plugin-solid": "^2.11.6"}, "dependencies": {"@codemirror/lang-markdown": "^6.3.2", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.37.1", "@pdfslick/solid": "^3.0.0", "@solidjs/router": "^0.15.3", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/vite": "^4.1.8", "axios": "^1.9.0", "codemirror": "^6.0.1", "lucide-solid": "^0.514.0", "marked": "^15.0.12", "pdfjs-dist": "^5.3.31", "solid-js": "^1.9.7", "solid-konva": "^0.1.6"}}