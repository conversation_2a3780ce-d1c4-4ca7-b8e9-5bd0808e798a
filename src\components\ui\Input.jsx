import { splitProps } from 'solid-js';

const Input = (props) => {
  const [local, others] = splitProps(props, [
    'label',
    'error',
    'helperText',
    'leftIcon',
    'rightIcon',
    'rightElement',
    'size',
    'variant',
    'class',
    'containerClass'
  ]);

  const baseClasses = 'block w-full rounded-lg border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-0 input-theme';

  const variants = {
    default: '',
    error: 'border-theme-error focus:border-theme-error focus:ring-theme-error',
    success: 'border-theme-success focus:border-theme-success focus:ring-theme-success'
  };

  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  };

  const variant = local.error ? 'error' : (local.variant || 'default');
  const size = local.size || 'md';

  const inputClasses = `${baseClasses} ${variants[variant]} ${sizes[size]} ${
    local.leftIcon ? 'pl-10' : ''
  } ${local.rightIcon ? 'pr-10' : ''} ${local.rightElement ? 'pr-20' : ''} ${local.class || ''}`;

  return (
    <div class={local.containerClass}>
      {local.label && (
        <label class="block text-sm font-medium text-theme-primary mb-1">
          {local.label}
        </label>
      )}
      
      <div class="relative">
        {local.leftIcon && (
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <div class="text-theme-muted">
              {local.leftIcon}
            </div>
          </div>
        )}
        
        <input
          class={inputClasses}
          {...others}
        />
        
        {local.rightIcon && (
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <div class="text-theme-muted">
              {local.rightIcon}
            </div>
          </div>
        )}

        {local.rightElement && (
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
            {local.rightElement}
          </div>
        )}
      </div>
      
      {local.error && (
        <p class="mt-1 text-sm text-theme-error">{local.error}</p>
      )}

      {local.helperText && !local.error && (
        <p class="mt-1 text-sm text-theme-muted">{local.helperText}</p>
      )}
    </div>
  );
};

export default Input;
