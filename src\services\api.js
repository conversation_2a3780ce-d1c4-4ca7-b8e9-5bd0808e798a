import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: '/api', // 使用Vite代理
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('Response Error:', error.response?.status, error.response?.data);
    
    // 统一错误处理
    const message = error.response?.data?.message || error.message || '请求失败';
    
    // 可以在这里添加全局错误提示
    // toast.error(message);
    
    return Promise.reject(new Error(message));
  }
);

// 健康检查
export const healthCheck = async () => {
  try {
    const response = await axios.get('/health');
    return response.data;
  } catch (error) {
    throw new Error('后端服务连接失败');
  }
};

// 文献相关API
export const papersAPI = {
  // 获取文献列表
  getList: (params = {}) => api.get('/papers', { params }),
  
  // 获取文献详情
  getById: (id) => api.get(`/papers/${id}`),
  
  // 创建文献
  create: (data) => api.post('/papers', data),
  
  // 更新文献
  update: (id, data) => api.put(`/papers/${id}`, data),
  
  // 删除文献
  delete: (id) => api.delete(`/papers/${id}`),

  // 获取统计信息
  getStats: () => api.get('/papers/stats/detailed'),
};

// 文件相关API
export const filesAPI = {
  // 获取文献文件列表
  getList: (paperId) => api.get(`/papers/${paperId}/files`),
  
  // 上传文件
  upload: (paperId, file, type = 'note') => {
    const formData = new FormData();
    formData.append('file', file);

    return api.post(`/papers/${paperId}/files?type=${type}`, formData, {
      headers: {
        // 完全移除 Content-Type，让浏览器自动设置
      },
      // 确保不使用默认的JSON转换
      transformRequest: [(data) => data],
    });
  },
  
  // 下载文件
  download: (paperId, filename) => {
    return api.get(`/papers/${paperId}/files/${filename}`, {
      responseType: 'blob',
    });
  },
  
  // 删除文件
  delete: (paperId, filename) => api.delete(`/papers/${paperId}/files/${filename}`),

  // 重命名文件
  rename: (paperId, filename, newName) => {
    return api.put(`/papers/${paperId}/files/${encodeURIComponent(filename)}`, {
      new_name: newName
    });
  },
};

// 文件夹相关API
export const foldersAPI = {
  // 获取文件夹列表
  getList: (params = {}) => api.get('/folders', { params }),
  
  // 获取文件夹树
  getTree: () => api.get('/papers/folders/tree'),
  
  // 创建文件夹
  create: (data) => api.post('/folders', data),
  
  // 重命名文件夹
  rename: (path, newName) => api.put(`/folders/${encodeURIComponent(path)}`, { new_name: newName }),
  
  // 删除文件夹
  delete: (path) => api.delete(`/folders/${encodeURIComponent(path)}`),
};

// 缓存相关API
export const cacheAPI = {
  // 获取缓存状态
  getStats: () => api.get('/papers/cache/stats'),
  
  // 重新加载缓存
  reload: () => api.post('/papers/cache/reload'),
};

export default api;
