import { splitProps } from 'solid-js';

const Textarea = (props) => {
  const [local, others] = splitProps(props, [
    'label',
    'error',
    'helperText',
    'size',
    'variant',
    'resize',
    'autoResize',
    'class',
    'containerClass'
  ]);

  const baseClasses = 'block w-full rounded-lg border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-0 input-theme';

  const variants = {
    default: '',
    error: 'border-theme-error focus:border-theme-error focus:ring-theme-error',
    success: 'border-theme-success focus:border-theme-success focus:ring-theme-success'
  };

  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  };

  const resizeClasses = {
    none: 'resize-none',
    vertical: 'resize-y',
    horizontal: 'resize-x',
    both: 'resize'
  };

  const variant = local.error ? 'error' : (local.variant || 'default');
  const size = local.size || 'md';
  const resize = local.resize || 'vertical';

  const textareaClasses = `${baseClasses} ${variants[variant]} ${sizes[size]} ${resizeClasses[resize]} ${local.class || ''}`;

  // 自动调整高度
  const handleInput = (e) => {
    if (local.autoResize) {
      e.target.style.height = 'auto';
      e.target.style.height = e.target.scrollHeight + 'px';
    }
    
    // 调用原始的 onInput 事件
    if (others.onInput) {
      others.onInput(e);
    }
  };

  return (
    <div class={local.containerClass}>
      {local.label && (
        <label class="block text-sm font-medium text-theme-primary mb-1">
          {local.label}
        </label>
      )}

      <textarea
        class={textareaClasses}
        onInput={handleInput}
        {...others}
      />

      {local.error && (
        <p class="mt-1 text-sm text-theme-error">{local.error}</p>
      )}

      {local.helperText && !local.error && (
        <p class="mt-1 text-sm text-theme-muted">{local.helperText}</p>
      )}
    </div>
  );
};

export default Textarea;
