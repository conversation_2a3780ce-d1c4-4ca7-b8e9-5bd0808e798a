import { createEffect, createSignal } from 'solid-js';
import { usePaperContext } from '../../stores/PaperContext';
import PaperGrid from '../features/PaperGrid';
import PaperToolbar from '../features/PaperToolbar';
import PaperBatchActions from '../features/PaperBatchActions';
import MovePapersModal from '../features/MovePapersModal';
import { Modal, Button, toast, Pagination } from '../ui';

const MainContent = (props) => {
  // 使用PaperContext
  const {
    papers,
    loading,
    error,
    selectedPapers,
    selectMode,
    filters,
    pagination,
    setSearch,
    updateFilters,
    togglePaperSelection,
    clearSelection,
    setSelectMode,
    loadPapers,
    deleteSelectedPapers,
    changePage,
    changePageSize
  } = usePaperContext();

  // 模态框状态
  const [showMoveModal, setShowMoveModal] = createSignal(false);
  const [showDeleteModal, setShowDeleteModal] = createSignal(false);

  // 监听全局搜索查询变化
  createEffect((prev) => {
    const currentQuery = props.globalSearchQuery || '';

    // 如果是第一次运行，不执行搜索
    if (prev === undefined) {
      return currentQuery;
    }

    // 只有当查询真正变化时才重新搜索
    if (prev !== currentQuery) {
      setSearch(currentQuery);
    }

    return currentQuery;
  });

  // 切换选择模式
  const toggleSelectMode = () => {
    setSelectMode(!selectMode());
    if (!selectMode()) {
      clearSelection();
    }
  };

  // 处理排序变化
  const handleSortChange = (sortBy) => {
    updateFilters({ sort_by: sortBy });
  };

  // 处理排序顺序变化
  const handleSortOrderToggle = () => {
    const newOrder = filters().sort_order === 'asc' ? 'desc' : 'asc';
    updateFilters({ sort_order: newOrder });
  };

  // 处理年份筛选
  const handleYearStartChange = (value) => {
    const yearStart = value ? parseInt(value) : null;
    updateFilters({ year_start: yearStart });
  };

  const handleYearEndChange = (value) => {
    const yearEnd = value ? parseInt(value) : null;
    updateFilters({ year_end: yearEnd });
  };

  const handleClearYearFilter = () => {
    updateFilters({ year_start: null, year_end: null });
  };

  // 处理移动文献
  const handleMovePapers = () => {
    if (selectedPapers().size === 0) {
      toast.error('请先选择要移动的文献');
      return;
    }
    setShowMoveModal(true);
  };

  // 处理删除文献
  const handleDeletePapers = () => {
    if (selectedPapers().size === 0) {
      toast.error('请先选择要删除的文献');
      return;
    }
    setShowDeleteModal(true);
  };

  // 确认删除文献
  const handleConfirmDelete = async () => {
    try {
      await deleteSelectedPapers();
      toast.success(`已成功删除 ${selectedPapers().size} 篇文献`);
      setShowDeleteModal(false);
    } catch (err) {
      console.error('删除文献失败:', err);
      toast.error(err.message || '删除文献失败，请重试');
    }
  };

  // 移动成功回调
  const handleMoveSuccess = () => {
    setShowMoveModal(false);
    clearSelection();
  };

  // 分页处理函数
  const handlePageChange = (page) => {
    changePage(page);
  };

  const handlePageSizeChange = (_, pageSize) => {
    changePageSize(pageSize);
  };



  return (
    <main class={`flex-1 flex flex-col bg-theme-secondary transition-all duration-300 min-h-0 ${
      props.drawerOpen ? `mr-${Math.floor(props.drawerWidth / 16)}` : ''
    }`}>
      {/* 批量操作栏 */}
      {console.log('MainContent - selectedPapers size:', selectedPapers().size, 'selectMode:', selectMode())}
      <PaperBatchActions
        action={selectMode()}
        selectedCount={selectedPapers().size}
        onClearSelection={clearSelection}
        onMove={handleMovePapers}
        onDelete={handleDeletePapers}
      />

      {/* 工具栏 */}
      <PaperToolbar
        sortBy={filters().sort_by}
        sortOrder={filters().sort_order}
        selectMode={selectMode()}
        yearStart={filters().year_start}
        yearEnd={filters().year_end}
        onSortByChange={handleSortChange}
        onSortOrderToggle={handleSortOrderToggle}
        onSelectModeToggle={toggleSelectMode}
        onYearStartChange={handleYearStartChange}
        onYearEndChange={handleYearEndChange}
        onClearYearFilter={handleClearYearFilter}
      />

      {/* 内容区域 */}
      <div class="flex-1 flex flex-col min-h-0">
        <div class="flex-1 overflow-y-auto p-6 min-h-0">
          <PaperGrid
            papers={papers()}
            loading={loading()}
            error={error()}
            selectedPapers={selectedPapers()}
            selectMode={selectMode()}
            onPaperSelect={props.onPaperSelect}
            onToggleSelection={togglePaperSelection}
            onRetry={() => loadPapers()}
          />
        </div>

        {/* 分页组件 */}
        {!loading() && !error() && papers().length > 0 && (
          <div class="border-t border-theme-border bg-theme-primary px-6 py-4">
            <Pagination
              current={pagination().page}
              total={pagination().total}
              pageSize={pagination().page_size}
              showSizeChanger={true}
              showQuickJumper={true}
              showTotal={true}
              onChange={handlePageChange}
              onShowSizeChange={handlePageSizeChange}
              pageSizeOptions={[10, 20, 50, 100]}
              size="md"
            />
          </div>
        )}
      </div>

      {/* 移动文献模态框 */}
      <MovePapersModal
        open={showMoveModal()}
        onClose={() => setShowMoveModal(false)}
        onSuccess={handleMoveSuccess}
        selectedPapers={Array.from(selectedPapers())}
      />

      {/* 删除确认模态框 */}
      <Modal
        open={showDeleteModal()}
        onClose={() => setShowDeleteModal(false)}
        title="删除文献"
        size="sm"
      >
        <div class="space-y-4">
          <p class="text-theme-secondary">
            确定要删除选中的 <span class="font-medium text-theme-primary">{selectedPapers().size}</span> 篇文献吗？
          </p>
          <p class="text-sm text-theme-error">
            此操作不可撤销，文献及其所有相关文件都将被永久删除。
          </p>
          <div class="flex justify-end space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
            >
              取消
            </Button>
            <Button
              variant="danger"
              onClick={handleConfirmDelete}
            >
              确认删除
            </Button>
          </div>
        </div>
      </Modal>
    </main>
  );
};

export default MainContent;
