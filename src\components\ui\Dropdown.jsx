import { createSignal, Show, onCleanup, splitProps } from 'solid-js';
import { ChevronDown } from 'lucide-solid';

const Dropdown = (props) => {
  const [local, others] = splitProps(props, [
    'children',
    'trigger',
    'placement',
    'disabled',
    'class',
    'triggerClass',
    'menuClass'
  ]);

  const [isOpen, setIsOpen] = createSignal(false);
  let dropdownRef;
  let triggerRef;

  const placement = local.placement || 'bottom-start';

  // 点击外部关闭下拉菜单
  const handleClickOutside = (event) => {
    if (dropdownRef && !dropdownRef.contains(event.target)) {
      setIsOpen(false);
    }
  };

  // 监听点击事件
  document.addEventListener('click', handleClickOutside);
  onCleanup(() => {
    document.removeEventListener('click', handleClickOutside);
  });

  const toggleDropdown = () => {
    if (!local.disabled) {
      setIsOpen(!isOpen());
    }
  };

  const getMenuPosition = () => {
    const positions = {
      'bottom-start': 'top-full left-0 mt-1',
      'bottom-end': 'top-full right-0 mt-1',
      'top-start': 'bottom-full left-0 mb-1',
      'top-end': 'bottom-full right-0 mb-1',
      'left': 'right-full top-0 mr-1',
      'right': 'left-full top-0 ml-1'
    };
    return positions[placement] || positions['bottom-start'];
  };

  return (
    <div 
      ref={dropdownRef}
      class={`relative inline-block ${local.class || ''}`}
      {...others}
    >
      {/* 触发器 */}
      <div
        ref={triggerRef}
        onClick={toggleDropdown}
        class={`cursor-pointer ${local.disabled ? 'opacity-50 cursor-not-allowed' : ''} ${local.triggerClass || ''}`}
      >
        {local.trigger || (
          <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-neutral-500">
            选择
            <ChevronDown size={16} class="ml-2" />
          </button>
        )}
      </div>

      {/* 下拉菜单 */}
      <Show when={isOpen()}>
        <div class={`absolute z-50 ${getMenuPosition()} ${local.menuClass || ''}`}>
          <div class="bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-48">
            {local.children}
          </div>
        </div>
      </Show>
    </div>
  );
};

// 下拉菜单项组件
const DropdownItem = (props) => {
  const [local, others] = splitProps(props, [
    'children',
    'onClick',
    'disabled',
    'danger',
    'class'
  ]);

  const handleClick = () => {
    if (!local.disabled && local.onClick) {
      local.onClick();
    }
  };

  const baseClasses = 'block w-full text-left px-4 py-2 text-sm transition-colors duration-150';
  const stateClasses = local.disabled 
    ? 'text-gray-400 cursor-not-allowed'
    : local.danger
      ? 'text-red-700 hover:bg-red-50'
      : 'text-gray-700 hover:bg-gray-100';

  return (
    <button
      class={`${baseClasses} ${stateClasses} ${local.class || ''}`}
      onClick={handleClick}
      disabled={local.disabled}
      {...others}
    >
      {local.children}
    </button>
  );
};

// 下拉菜单分隔线
const DropdownDivider = () => (
  <div class="border-t border-gray-200 my-1" />
);

export { Dropdown as default, DropdownItem, DropdownDivider };
