import { createMemo, For, splitProps } from 'solid-js';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-solid';
import Button from './Button';
import Select from './Select';
import NumberInput from './NumberInput';

const Pagination = (props) => {
  const [local, others] = splitProps(props, [
    'current',
    'total',
    'pageSize',
    'showSizeChanger',
    'showQuickJumper',
    'showTotal',
    'onChange',
    'onShowSizeChange',
    'pageSizeOptions',
    'size',
    'class'
  ]);

  const pageSize = () => local.pageSize || 10;
  const current = () => local.current || 1;
  const total = () => local.total || 0;
  
  const totalPages = createMemo(() => Math.ceil(total() / pageSize()));
  
  const pageSizeOptions = () => local.pageSizeOptions || [10, 20, 50, 100];
  
  const size = local.size || 'md';

  const sizes = {
    sm: {
      button: 'px-2 py-1 text-xs min-w-8 h-8',
      input: 'px-2 py-1 text-xs w-12 h-8',
      select: 'px-2 py-1 text-xs h-8'
    },
    md: {
      button: 'px-3 py-2 text-sm min-w-10 h-10',
      input: 'px-3 py-2 text-sm w-16 h-10',
      select: 'px-3 py-2 text-sm h-10'
    },
    lg: {
      button: 'px-4 py-2 text-base min-w-12 h-12',
      input: 'px-4 py-2 text-base w-20 h-12',
      select: 'px-4 py-2 text-base h-12'
    }
  };

  // 生成页码数组 - 连续显示5个页码按钮，以当前页为中心
  const getPageNumbers = createMemo(() => {
    const pages = [];
    const currentPage = current();
    const totalPageCount = totalPages();

    if (totalPageCount <= 5) {
      // 如果总页数≤5，显示所有页码：1 2 3 4 5
      for (let i = 1; i <= totalPageCount; i++) {
        pages.push(i);
      }
    } else {
      // 连续显示5个页码，以当前页为中心
      let startPage, endPage;

      if (currentPage <= 2) {
        // 当前页在前面：1 2 3 4 5
        startPage = 1;
        endPage = 5;
      } else if (currentPage >= totalPageCount - 1) {
        // 当前页在后面：倒数4 倒数3 倒数2 倒数1 最后页
        startPage = totalPageCount - 4;
        endPage = totalPageCount;
      } else {
        // 当前页在中间：前2 前1 当前 后1 后2
        startPage = currentPage - 2;
        endPage = currentPage + 2;
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }

    return pages;
  });

  const handlePageChange = (page) => {
    if (page !== current() && page >= 1 && page <= totalPages() && local.onChange) {
      local.onChange(page, pageSize());
    }
  };

  const handlePageSizeChange = (newPageSize) => {
    if (local.onShowSizeChange) {
      local.onShowSizeChange(current(), newPageSize);
    }
  };



  if (totalPages() <= 1) {
    return null;
  }

  return (
    <div class={`flex items-center justify-between ${local.class || ''}`} {...others}>
      {/* 总数显示 */}
      {local.showTotal && (
        <div class="text-sm text-theme-secondary font-medium">
          共 <span class="text-theme-primary font-semibold">{total()}</span> 条记录
        </div>
      )}

      <div class="flex items-center space-x-2">
        {/* 页面大小选择器 */}
        {local.showSizeChanger && (
          <div class="flex items-center space-x-2 text-sm">
            <span class="text-theme-secondary whitespace-nowrap">每页</span>
            <Select
              value={pageSize()}
              onSelectionChange={(value) => handlePageSizeChange(parseInt(value))}
              options={pageSizeOptions().map(option => ({
                value: option,
                label: `${option} 条`
              }))}
              size="sm"
              class="w-24"
              dropdownDirection="up"
            />
          </div>
        )}

        {/* 分页按钮 */}
        <div class="flex items-center space-x-1">
          {/* 上一页 */}
          <Button
            onClick={() => handlePageChange(current() - 1)}
            disabled={current() === 1}
            variant="outline"
            size={size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'md'}
            class={`${sizes[size].button} min-w-10`}
            title="上一页"
          >
            <ChevronLeft size={16} />
          </Button>

          {/* 页码 */}
          <For each={getPageNumbers()}>
            {(page) => (
              page === '...' ? (
                <span class={`${sizes[size].button} text-theme-muted flex items-center justify-center`}>
                  <MoreHorizontal size={16} />
                </span>
              ) : (
                <Button
                  onClick={() => handlePageChange(page)}
                  variant={current() === page ? 'primary' : 'outline'}
                  size={size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'md'}
                  class={`${sizes[size].button} min-w-10`}
                  title={`第 ${page} 页`}
                >
                  {page}
                </Button>
              )
            )}
          </For>

          {/* 下一页 */}
          <Button
            onClick={() => handlePageChange(current() + 1)}
            disabled={current() === totalPages()}
            variant="outline"
            size={size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'md'}
            class={`${sizes[size].button} min-w-10`}
            title="下一页"
          >
            <ChevronRight size={16} />
          </Button>
        </div>

        {/* 快速跳转 */}
        {local.showQuickJumper && (
          <div class="flex items-center space-x-2 text-sm">
            <span class="text-theme-secondary whitespace-nowrap">跳至</span>
            <NumberInput
              min={1}
              max={totalPages()}
              onValueChange={(value) => {
                if (value && value >= 1 && value <= totalPages()) {
                  handlePageChange(value);
                }
              }}
              placeholder="页码"
              size="sm"
              class="w-12"
              showControls={false}
              allowEmpty={true}
            />
            <span class="text-theme-secondary whitespace-nowrap">页</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default Pagination;
