import { createMemo, splitProps, For } from 'solid-js';

/**
 * 高亮文本组件
 * 支持在文本中高亮显示搜索关键词
 */
const HighlightText = (props) => {
  const [local, others] = splitProps(props, [
    'text',
    'searchQuery',
    'highlightClass',
    'caseSensitive',
    'wholeWord',
    'class'
  ]);

  // 默认高亮样式
  const defaultHighlightClass = 'highlight-theme';

  // 处理高亮逻辑，返回文本片段数组
  const textParts = createMemo(() => {
    const text = local.text || '';
    const query = local.searchQuery || '';

    // 如果没有搜索查询或文本，直接返回原文本
    if (!query.trim() || !text) {
      return [{ text, highlight: false }];
    }

    // 分割搜索查询为多个关键词（支持空格分隔）
    const keywords = query.trim().split(/\s+/).filter(keyword => keyword.length > 0);

    if (keywords.length === 0) {
      return [{ text, highlight: false }];
    }

    // 构建联合正则表达式
    const escapedKeywords = keywords.map(keyword =>
      keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    );

    const flags = local.caseSensitive ? 'g' : 'gi';
    let pattern;

    if (local.wholeWord) {
      pattern = new RegExp(`\\b(${escapedKeywords.join('|')})\\b`, flags);
    } else {
      pattern = new RegExp(`(${escapedKeywords.join('|')})`, flags);
    }

    // 分割文本
    const parts = [];
    let lastIndex = 0;
    let match;

    while ((match = pattern.exec(text)) !== null) {
      // 添加匹配前的文本
      if (match.index > lastIndex) {
        parts.push({
          text: text.slice(lastIndex, match.index),
          highlight: false
        });
      }

      // 添加匹配的文本
      parts.push({
        text: match[0],
        highlight: true
      });

      lastIndex = match.index + match[0].length;
    }

    // 添加剩余的文本
    if (lastIndex < text.length) {
      parts.push({
        text: text.slice(lastIndex),
        highlight: false
      });
    }

    return parts;
  });

  const highlightClass = local.highlightClass || defaultHighlightClass;

  return (
    <span class={local.class || ''} {...others}>
      <For each={textParts()}>
        {(part) =>
          part.highlight ? (
            <mark class={highlightClass}>{part.text}</mark>
          ) : (
            part.text
          )
        }
      </For>
    </span>
  );
};

export default HighlightText;
